#include "ns3/core-module.h"
#include "ns3/network-module.h"
#include "ns3/mobility-module.h"
#include "ns3/internet-module.h"
#include "ns3/wifi-module.h"
#include "ns3/applications-module.h"
#include "ns3/ipv4-address-helper.h"
#include "ns3/ssid.h"
#include "ns3/flow-monitor-module.h"
#include <vector>
#include <cmath>
#include <map>
#include <set>
#include <algorithm>
#include <iostream>
#include <thread>
#include <chrono>

using namespace ns3;
using namespace std;

NS_LOG_COMPONENT_DEFINE ("ApNotUse");

#define PI 3.14159265358979323846

//阶梯教室参数（环扇形布局）
const double kClassroomRadiusMin = 6.0;    //最内圈半径(米) - 讲台区域
const double kClassroomRadiusMax = 20.0;   //最外圈半径(米) - 增加到20米以容纳更多排
const double kClassroomAngle = 120.0;      //扇形角度(度)
const double kRowSpacing = 1.2;            //阶梯行间距(米)
const double kSeatSpacing = 0.8;           //座位间距(米)
const int kRowsCount = 12;                 //阶梯行数 - 增加到12排
const double kStepHeight = 0.3;            //每级台阶高度(米)
const int kNumStudents = 200;
const int kNumClients = 400;
const int kNumAps = 5;
const int kNumHighPowerAps = 3;
const int kNumLowPowerAps = 2;
const double kLowPowerDbm = 12.0;
const double kHighPowerDbm = 16.0;
const int kMaxStaPerLowAp = 100;  //后排低功率AP容量
const int kMaxStaPerHighAp = 150; //前排高功率AP容量
const int kNumHighTrafficClients = 70; //高流量客户端数
const int kNumLateStudents = 5; //迟到学生数，假设每个学生有两个终端
Ptr<PropagationLossModel> g_lossModel;
// AP布局
struct ApInfo {
    Vector pos;
    double txPowerDbm;
    Ptr<Node> node;
    Ptr<WifiNetDevice> device;
    set<Mac48Address> blacklist;
    int maxSta;
};

struct StudentInfo {
    Vector pos;
};

// 客户端类型枚举
enum ClientType {
    NORMAL = 0,           // 普通客户端
    HIGH_TRAFFIC_ONLY = 1, // 仅高流量
    HIGH_DELAY_ONLY = 2,   // 仅高延迟
    HIGH_TRAFFIC_AND_DELAY = 3 // 高流量+高延迟
};

struct ClientInfo {
    int studentIdx; //所属学生
    Ptr<Node> node;
    Ptr<WifiNetDevice> device;
    Mac48Address mac;
    Ipv4Address ip;
    bool isHighTraffic;
    bool isHighDelay; //是否为高延迟客户端
    ClientType clientType; //客户端类型
    int connectedApIdx;
};

vector<ApInfo> apInfos;
vector<StudentInfo> studentInfos;
vector<ClientInfo> clientInfos;
map<Mac48Address, int> clientToAp; //客户端 MAC -> AP idx

void SimulationDelay(int milliseconds) {
    std::this_thread::sleep_for(std::chrono::milliseconds(milliseconds));
}

//发送802.11解除关联帧
void SendDisassociationFrame(int apIdx, const Mac48Address& staMac, const string& reason) {
    if (apIdx < 0 || apIdx >= (int)apInfos.size()) {
        cout << "错误：AP索引无效 " << apIdx << endl;
        return;
    }

    Ptr<WifiNetDevice> apDevice = apInfos[apIdx].device;
    if (!apDevice) {
        cout << "错误：AP" << apIdx << " 设备为空" << endl;
        return;
    }

    //获取AP的MAC层
    Ptr<WifiMac> apMac = apDevice->GetMac();
    Ptr<ApWifiMac> apWifiMac = DynamicCast<ApWifiMac>(apMac);

    if (!apWifiMac) {
        cout << "错误：无法获取AP" << apIdx << " 的MAC层" << endl;
        return;
    }

    cout << "AP" << apIdx << " 向客户端 " << staMac << " 发送解除关联帧 (原因: " << reason << ")" << endl;

    //获取AP的MAC地址
    Mac48Address apMacAddr = Mac48Address::ConvertFrom(apDevice->GetAddress());

    //创建解除关联帧
    WifiMacHeader hdr;
    hdr.SetType(WIFI_MAC_MGT_DISASSOCIATION);
    hdr.SetAddr1(staMac);     //目标地址（STA）
    hdr.SetAddr2(apMacAddr);  //源地址（AP）
    hdr.SetAddr3(apMacAddr);  //BSSID
    hdr.SetDsNotFrom();
    hdr.SetDsNotTo();

    //创建解除关联帧体
    Ptr<Packet> packet = Create<Packet>(2); //2字节的原因码
    uint16_t reasonCode = 3; //解除关联原因：STA离开
    packet->AddHeader(hdr);

    cout << "解除关联帧已准备发送 (原因码: " << reasonCode << ")" << endl;
    SimulationDelay(100); //仿真帧传输延迟
}

//发送802.11解除认证帧的函数
void SendDeauthenticationFrame(int apIdx, const Mac48Address& staMac, const string& reason) {
    if (apIdx < 0 || apIdx >= (int)apInfos.size()) {
        cout << "错误：AP索引无效 " << apIdx << endl;
        return;
    }

    Ptr<WifiNetDevice> apDevice = apInfos[apIdx].device;
    if (!apDevice) {
        cout << "错误：AP" << apIdx << " 设备为空" << endl;
        return;
    }

    //获取AP的MAC层
    Ptr<WifiMac> apMac = apDevice->GetMac();
    Ptr<ApWifiMac> apWifiMac = DynamicCast<ApWifiMac>(apMac);

    if (!apWifiMac) {
        cout << "错误：无法获取AP" << apIdx << " 的MAC层" << endl;
        return;
    }

    cout << "AP" << apIdx << " 向客户端 " << staMac << " 发送解除认证帧 (原因: " << reason << ")" << endl;

    //获取AP的MAC地址
    Mac48Address apMacAddr = Mac48Address::ConvertFrom(apDevice->GetAddress());

    //创建解除认证帧
    WifiMacHeader hdr;
    hdr.SetType(WIFI_MAC_MGT_DEAUTHENTICATION);
    hdr.SetAddr1(staMac);     //目标地址（STA）
    hdr.SetAddr2(apMacAddr);  //源地址（AP）
    hdr.SetAddr3(apMacAddr);  //BSSID
    hdr.SetDsNotFrom();
    hdr.SetDsNotTo();

    //创建解除认证帧体
    Ptr<Packet> packet = Create<Packet>(2); //2字节的原因码
    uint16_t reasonCode = 2; //解除认证原因：之前的认证无效
    packet->AddHeader(hdr);

    cout << "解除认证帧已准备发送 (原因码: " << reasonCode << ")" << endl;
    SimulationDelay(100); //模拟帧传输延迟
}

//实现黑名单机制：先加入黑名单，再发送解除关联帧，最后断开连接
void ApplyBlacklistWithDisassociation(int apIdx, const Mac48Address& staMac, const string& reason) {
    cout << "\n 执行802.11黑名单机制:" << endl;
    SimulationDelay(200);

    //1.先将客户端加入黑名单（防止重连）
    apInfos[apIdx].blacklist.insert(staMac);
    cout << "客户端 " << staMac << " 已加入AP" << apIdx << " 黑名单" << endl;
    SimulationDelay(100); //确保黑名单生效

    //2.发送解除关联帧
    SendDisassociationFrame(apIdx, staMac, reason);

    //3.断开客户端连接（在仿真层面）
    for (auto& client : clientInfos) {
        if (client.mac == staMac && client.connectedApIdx == apIdx) {
            client.connectedApIdx = -1; //断开连接
            cout << "客户端 " << staMac << " 已从AP" << apIdx << " 断开连接" << endl;
            break;
        }
    }

    SimulationDelay(300);
    cout << "黑名单机制执行完成（顺序：黑名单->解除关联帧->断开连接）\n" << endl;
}

//AP布局初始化 - 环扇形阶梯教室布局
void InitApPositions() {
    //根据环扇形教室布局：前排2个高功率AP，后排3个低功率AP

    //AP0, AP1: 前排两侧
    double frontAngle1 = -25.0 * PI / 180.0; //左前方
    double frontAngle2 = 25.0 * PI / 180.0;  //右前方
    double frontRadius = 7.0; //前排半径较小
    double frontHeight = 2.5;
    apInfos.push_back({Vector(frontRadius * sin(frontAngle1), frontRadius * cos(frontAngle1), frontHeight),
                      kHighPowerDbm, nullptr, nullptr, {}, kMaxStaPerHighAp});
    apInfos.push_back({Vector(frontRadius * sin(frontAngle2), frontRadius * cos(frontAngle2), frontHeight),
                      kHighPowerDbm, nullptr, nullptr, {}, kMaxStaPerHighAp});

    // AP2, AP3, AP4: 后排三个位置（环扇形较宽的地方，低功率）
    double backAngle1 = -50.0 * PI / 180.0; //左后方
    double backAngle2 = 0.0 * PI / 180.0;   //正后方
    double backAngle3 = 50.0 * PI / 180.0;  //右后方
    double backRadius = 16.0; //后排半径较大
    double backHeight = 4.0;  //后排安装高度更高
    apInfos.push_back({Vector(backRadius * sin(backAngle1), backRadius * cos(backAngle1), backHeight),
                      kLowPowerDbm, nullptr, nullptr, {}, kMaxStaPerLowAp});
    apInfos.push_back({Vector(backRadius * sin(backAngle2), backRadius * cos(backAngle2), backHeight),
                      kLowPowerDbm, nullptr, nullptr, {}, kMaxStaPerLowAp});
    apInfos.push_back({Vector(backRadius * sin(backAngle3), backRadius * cos(backAngle3), backHeight),
                      kLowPowerDbm, nullptr, nullptr, {}, kMaxStaPerLowAp});

    cout << "\n=== AP布局初始化 ===" << endl;
    SimulationDelay(500);
    cout << "环扇形阶梯教室AP布局:" << endl;
    SimulationDelay(300);
    cout << "前排AP（高功率）:" << endl;
    for (int i = 0; i < 2; ++i) {
        cout << "  AP" << i << ": 位置(" << apInfos[i].pos.x << ", " << apInfos[i].pos.y
             << ", " << apInfos[i].pos.z << "), 功率=" << apInfos[i].txPowerDbm << "dBm (高功率)" << endl;
        SimulationDelay(200);
    }
    cout << "后排AP（低功率）:" << endl;
    for (int i = 2; i < 5; ++i) {
        cout << "  AP" << i << ": 位置(" << apInfos[i].pos.x << ", " << apInfos[i].pos.y
             << ", " << apInfos[i].pos.z << "), 功率=" << apInfos[i].txPowerDbm << "dBm (低功率)" << endl;
        SimulationDelay(200);
    }
    cout << "=== AP布局完成 ===\n" << endl;
    SimulationDelay(800);
}

//学生位置初始化 - 环扇形阶梯教室布局
void InitStudentPositions() {
    cout << "\n=== 学生位置初始化 ===" << endl;
    SimulationDelay(500);
    cout << "正在初始化环扇形阶梯教室学生位置..." << endl;
    SimulationDelay(300);

    // 第一排半径比教室内半径稍大一点（讲台与第一排之间的距离）
    const double kFirstRowRadius = kClassroomRadiusMin + 0.8; // 比内半径大0.8米

    // 预定义每排的人数，确保递增且总数为200
    // 前2排不坐人，从第3排开始：15, 18, 21, 25, 28, 31, 34, 37, 40, 43
    vector<int> seatsPerRow = {0, 0, 15, 18, 21, 25, 28, 31, 34, 37, 40, 43}; // 12排

    // 验证总人数
    int totalSeats = 0;
    for (int seats : seatsPerRow) {
        totalSeats += seats;
    }
    cout << "计划总座位数: " << totalSeats << " (需要200个学生)" << endl;

    // 如果总数不是200，调整最后几排
    if (totalSeats != kNumStudents) {
        int diff = kNumStudents - totalSeats;
        cout << "调整座位数差值: " << diff << endl;
        // 从后往前调整
        for (int i = seatsPerRow.size() - 1; i >= 2 && diff != 0; --i) {
            if (diff > 0) {
                int add = min(diff, 5); // 每排最多增加5个
                seatsPerRow[i] += add;
                diff -= add;
            } else {
                int sub = min(-diff, 3); // 每排最多减少3个
                seatsPerRow[i] = max(0, seatsPerRow[i] - sub);
                diff += sub;
            }
        }
    }

    //计算每行的半径
    double radiusStep = (kClassroomRadiusMax - kFirstRowRadius) / (kRowsCount - 1);
    int studentsPlaced = 0;

    for (int row = 0; row < kRowsCount && studentsPlaced < kNumStudents; ++row) {
        double radius;
        if (row == 0) {
            radius = kFirstRowRadius; // 第一排使用特定半径
        } else {
            radius = kFirstRowRadius + (row - 1) * radiusStep;
        }
        double rowHeight = row * kStepHeight; //阶梯高度

        int seatsInRow = seatsPerRow[row]; // 使用预定义的座位数

        if (seatsInRow == 0) {
            cout << "  第" << (row + 1) << "排: 空排（前2排不坐学生）" << endl;
            SimulationDelay(100);
            continue;
        }

        //计算该行的角度间距
        double angleStep = kClassroomAngle / (seatsInRow + 1); // +1是为了在两端留空隙
        double startAngle = -kClassroomAngle / 2.0;

        cout << "  第" << (row + 1) << "排: 半径=" << radius << "m, 高度=" << rowHeight
             << "m, 座位数=" << seatsInRow << endl;
        SimulationDelay(100);

        for (int seat = 0; seat < seatsInRow && studentsPlaced < kNumStudents; ++seat) {
            double angle = startAngle + (seat + 1) * angleStep;
            double angleRad = angle * PI / 180.0;

            //计算学生位置（极坐标转直角坐标）
            double x = radius * sin(angleRad);
            double y = radius * cos(angleRad);
            double z = rowHeight;

            //添加小量随机偏移(±15cm)模拟真实座位的实际差异
            double offsetX = ((double)rand() / RAND_MAX - 0.5) * 0.3;
            double offsetY = ((double)rand() / RAND_MAX - 0.5) * 0.3;
            double offsetZ = ((double)rand() / RAND_MAX - 0.5) * 0.1; //Z方向偏移较小

            x += offsetX;
            y += offsetY;
            z += offsetZ;

            studentInfos.push_back({Vector(x, y, z)});
            studentsPlaced++;
        }
    }

    cout << "\n 成功初始化 " << studentsPlaced << " 个学生位置（环扇形阶梯布局）" << endl;
    SimulationDelay(300);
    cout << "教室参数: 内半径=" << kClassroomRadiusMin << "m, 第一排半径=" << kFirstRowRadius
         << "m, 外半径=" << kClassroomRadiusMax << "m, 扇形角度=" << kClassroomAngle << "度, 阶梯数=" << kRowsCount << endl;
    cout << "前2排空置，学生从第3排开始就座，每排人数递增" << endl;
    cout << "=== 学生位置初始化完成 ===\n" << endl;
    SimulationDelay(800);
}

//客户端初始化
void InitClientInfos() {
    //平均分配，每个学生至少2个客户端，部分学生3个
    int baseClients = kNumClients / kNumStudents; //2
    int extra = kNumClients % kNumStudents;       //100
    int idx = 0;
    for (int i = 0; i < kNumStudents; ++i) {
        int num = baseClients + (i < extra ? 1 : 0);
        for (int j = 0; j < num; ++j) {
            clientInfos.push_back({i, nullptr, nullptr, Mac48Address(), Ipv4Address(), false, false, NORMAL, -1});
            idx++;
        }
    }
}

//计算AP与客户端距离
double Distance(const Vector& a, const Vector& b) {
    return sqrt(pow(a.x - b.x, 2) + pow(a.y - b.y, 2));
}

//计算信号强度（ns-3信道模型）
double CalcRssi(const ApInfo& ap, const ClientInfo& client) {
    Vector apPos = ap.pos;
    Vector staPos = studentInfos[client.studentIdx].pos;
    Ptr<MobilityModel> apMob = CreateObject<ConstantPositionMobilityModel>();
    apMob->SetPosition(apPos);
    Ptr<MobilityModel> staMob = CreateObject<ConstantPositionMobilityModel>();
    staMob->SetPosition(staPos);
    double rxPowerDbm = g_lossModel->CalcRxPower(ap.txPowerDbm, apMob, staMob);
    return rxPowerDbm;
}

//选择信号最强的AP（不在黑名单）
int SelectBestAp(const ClientInfo& client) {
    double bestRssi = -1e9;
    int bestIdx = -1;
    for (size_t i = 0; i < apInfos.size(); ++i) {
        if (apInfos[i].blacklist.count(client.mac)) continue;
        double rssi = CalcRssi(apInfos[i], client);
        if (rssi > bestRssi) {
            bestRssi = rssi;
            bestIdx = i;
        }
    }
    return bestIdx;
}

//选择信号最强的AP（不在黑名单，考虑容量限制）
int SelectBestAp(const ClientInfo& client, const vector<int>& apStaCount) {
    double bestRssi = -1e9;
    int bestIdx = -1;
    for (size_t i = 0; i < apInfos.size(); ++i) {
        if (apInfos[i].blacklist.count(client.mac)) continue;
        if (apStaCount[i] >= apInfos[i].maxSta) continue; // 容量限制
        double rssi = CalcRssi(apInfos[i], client);
        if (rssi > bestRssi) {
            bestRssi = rssi;
            bestIdx = i;
        }
    }
    return bestIdx;
}

//计算客户端在指定AP上的综合评分（用于负载均衡决策）
double CalculateClientScore(int clientIdx, int apIdx, const vector<int>& apStaCount) {
    if (apIdx < 0 || apIdx >= (int)apInfos.size()) return -1e9;
    if (apInfos[apIdx].blacklist.count(clientInfos[clientIdx].mac)) return -1e9;
    if (apStaCount[apIdx] >= apInfos[apIdx].maxSta) return -1e9;

    Vector clientPos = studentInfos[clientInfos[clientIdx].studentIdx].pos;

    //计算距离因子（距离越近越好）
    double distance = Distance(apInfos[apIdx].pos, clientPos);
    double distanceFactor = 1.0 / (1.0 + distance * 0.1);

    //计算信号强度因子
    double rssi = CalcRssi(apInfos[apIdx], clientInfos[clientIdx]);
    double rssiNormalized = (rssi + 100.0) / 50.0;
    rssiNormalized = max(0.0, min(1.0, rssiNormalized));

    //计算负载均衡因子（当前负载越低越好）
    int targetLoad = kNumClients / kNumAps;
    size_t remainder = kNumClients % kNumAps;
    int currentTarget = targetLoad + (apIdx < (int)remainder ? 1 : 0);
    double loadFactor = 1.0 - (double)apStaCount[apIdx] / currentTarget;
    loadFactor = max(0.1, loadFactor);

    //根据学生位置调整权重：按半径距离判断前后排
    double positionBias = 1.0;
    double studentRadius = sqrt(clientPos.x * clientPos.x + clientPos.y * clientPos.y);

    if (studentRadius < 11.0) { //前排学生
        if (apIdx < 2) positionBias = 1.4; //前排AP加权
        else positionBias = 0.6;           //后排AP减权
    } else { //后排学生
        if (apIdx >= 2) positionBias = 1.4; //后排AP加权
        else positionBias = 0.6;            //前排AP减权
    }

    //综合评分：距离30%，信号强度30%，负载均衡30%，位置偏好10%
    double score = (0.3 * distanceFactor + 0.3 * rssiNormalized + 0.3 * loadFactor) * positionBias;

    return score;
}

//计算客户端重要性评分（用于负载均衡优先级决策）
double CalculateClientImportance(int clientIdx) {
    const ClientInfo& client = clientInfos[clientIdx];
    double importance = 1.0; // 基础重要性

    switch (client.clientType) {
        case NORMAL:
            importance = 1.0;
            break;
        case HIGH_TRAFFIC_ONLY:
            importance = 2.0; // 高流量客户端重要性较高
            break;
        case HIGH_DELAY_ONLY:
            importance = 1.8; // 高延迟客户端重要性中等
            break;
        case HIGH_TRAFFIC_AND_DELAY:
            importance = 3.0; // 双属性客户端重要性最高
            break;
    }

    return importance;
}

//获取客户端类型的字符串描述
string GetClientTypeString(ClientType type) {
    switch (type) {
        case NORMAL: return "普通";
        case HIGH_TRAFFIC_ONLY: return "高流量";
        case HIGH_DELAY_ONLY: return "高延迟";
        case HIGH_TRAFFIC_AND_DELAY: return "高流量+高延迟";
        default: return "未知";
    }
}

//输出AP连接情况
void PrintApStaStatus(string stage) {
    cout << "\n==== " << stage << " ====" << endl;
    SimulationDelay(600);
    for (size_t i = 0; i < apInfos.size(); ++i) {
        int cnt = 0;
        for (auto& client : clientInfos) {
            if ((size_t)client.connectedApIdx == i) ++cnt;
        }
        cout << "AP" << i << " (" << apInfos[i].pos.x << "," << apInfos[i].pos.y << ") 连接数: " << cnt << endl;
        SimulationDelay(300);
    }
    cout << "========================\n" << endl;
    SimulationDelay(1000);
}

//输出特殊客户端信息
void PrintSpecialClients() {
    cout << "\n=== 特殊客户端详细信息 ===" << endl;

    map<ClientType, vector<int>> typeGroups;
    for (size_t i = 0; i < clientInfos.size(); ++i) {
        if (clientInfos[i].clientType != NORMAL) {
            typeGroups[clientInfos[i].clientType].push_back(i);
        }
    }

    for (auto& pair : typeGroups) {
        cout << GetClientTypeString(pair.first) << "客户端 (" << pair.second.size() << "个):" << endl;
        for (int idx : pair.second) {
            cout << "  " << clientInfos[idx].ip << " / " << clientInfos[idx].mac
                 << " (重要性: " << CalculateClientImportance(idx) << ")" << endl;
        }
        cout << endl;
    }
}

//输出黑名单信息
void PrintBlacklist() {
    cout << "\n=== 802.11黑名单状态 ===" << endl;
    SimulationDelay(400);

    bool hasBlacklist = false;
    for (size_t i = 0; i < apInfos.size(); ++i) {
        if (!apInfos[i].blacklist.empty()) {
            hasBlacklist = true;
            cout << "AP" << i << " 黑名单 (" << apInfos[i].blacklist.size() << " 个客户端): ";
            for (auto& mac : apInfos[i].blacklist) {
                cout << mac << " ";
            }
            cout << endl;
            SimulationDelay(200);
        }
    }

    if (!hasBlacklist) {
        cout << "当前没有客户端在黑名单中" << endl;
    }

    cout << "========================\n" << endl;
    SimulationDelay(600);
}

//负载均衡：解除关联并重新分配
void DisassociateAndRebalance() {
    cout << "\n=== 开始第一层负载均衡（基于信号强度+容量限制） ===" << endl;

    //0.调整AP最大连接数以实现负载均衡兜底机制
    cout << "调整AP最大连接数以确保负载均衡..." << endl;
    SimulationDelay(300);

    // 前排2个AP：从150调整为82左右（理想每AP 80个）
    int newMaxForHighPowerAp = 85;
    // 后排3个AP：从100调整为82左右（理想每AP 80个）
    int newMaxForLowPowerAp = 80;

    cout << "原始AP容量配置:" << endl;
    for (size_t i = 0; i < apInfos.size(); ++i) {
        cout << "  AP" << i << ": " << apInfos[i].maxSta << " -> ";
        if (i < 2) { // 前排高功率AP
            apInfos[i].maxSta = newMaxForHighPowerAp;
        } else { // 后排低功率AP
            apInfos[i].maxSta = newMaxForLowPowerAp;
        }
        cout << apInfos[i].maxSta << endl;
        SimulationDelay(100);
    }

    // 验证总容量
    int totalCapacity = 2 * newMaxForHighPowerAp + 3 * newMaxForLowPowerAp;
    cout << "调整后总容量: " << totalCapacity << " (需要容纳400个客户端)" << endl;
    if (totalCapacity < kNumClients) {
        cout << "警告：总容量不足！需要进一步调整" << endl;
    }
    SimulationDelay(500);

    //1.解除所有AP上所有客户端的关联
    cout << "解除所有客户端连接..." << endl;
    for (size_t i = 0; i < apInfos.size(); ++i) {
        for (size_t j = 0; j < clientInfos.size(); ++j) {
            if ((size_t)clientInfos[j].connectedApIdx == i) {
                cout << "AP" << i << " 解除关联 客户端 " << clientInfos[j].mac << endl;
                clientInfos[j].connectedApIdx = -1;
            }
        }
    }

    //2.重新分配：每个STA基于接收信号强度选择最强AP（受容量限制）
    cout << "基于信号强度重新分配（受新容量限制）..." << endl;
    vector<int> apStaCount(apInfos.size(), 0);

    for (size_t i = 0; i < clientInfos.size(); ++i) {
        int bestAp = SelectBestAp(clientInfos[i], apStaCount);

        if (bestAp != -1) {
            clientInfos[i].connectedApIdx = bestAp;
            apStaCount[bestAp]++;
            cout << "客户端 " << clientInfos[i].mac << " 重新连接到 AP" << bestAp
                 << " (基于RSSI+容量限制)" << endl;
        } else {
            cout << "警告：客户端 " << clientInfos[i].mac << " 无法找到可用AP（所有AP已满或在黑名单中）" << endl;
        }
    }

    cout << "第一层负载均衡完成，各AP连接数:" << endl;
    for (size_t i = 0; i < apInfos.size(); ++i) {
        cout << "  AP" << i << ": " << apStaCount[i] << "/" << apInfos[i].maxSta << endl;
    }
    cout << "=== 第一层负载均衡完成 ===\n" << endl;
}

//统一的特殊客户端负载均衡（处理高流量、高延迟、双属性客户端）
void SpecialClientBalance() {
    cout << "\n=== 开始统一特殊客户端负载均衡 ===" << endl;

    // 清理所有特殊客户端的黑名单记录
    cout << "清理特殊客户端的历史黑名单记录..." << endl;
    for (size_t i = 0; i < clientInfos.size(); ++i) {
        if (clientInfos[i].clientType != NORMAL) {
            for (size_t j = 0; j < apInfos.size(); ++j) {
                if (apInfos[j].blacklist.erase(clientInfos[i].mac)) {
                    cout << "清理：" << GetClientTypeString(clientInfos[i].clientType)
                         << "客户端 " << clientInfos[i].mac << " 从AP" << j << " 黑名单移除" << endl;
                }
            }
        }
    }

    // 按客户端类型分组统计每个AP上的特殊客户端
    vector<vector<int>> apSpecialClients(apInfos.size());
    map<ClientType, int> typeCount;

    for (size_t i = 0; i < clientInfos.size(); ++i) {
        if (clientInfos[i].clientType != NORMAL && clientInfos[i].connectedApIdx >= 0) {
            apSpecialClients[clientInfos[i].connectedApIdx].push_back(i);
            typeCount[clientInfos[i].clientType]++;
        }
    }

    cout << "当前特殊客户端分布:" << endl;
    for (auto& pair : typeCount) {
        cout << "  " << GetClientTypeString(pair.first) << ": " << pair.second << " 个" << endl;
    }
    // 计算理想的特殊客户端分布（按重要性加权）
    int totalSpecialClients = 0;
    double totalImportance = 0.0;

    for (size_t i = 0; i < clientInfos.size(); ++i) {
        if (clientInfos[i].clientType != NORMAL && clientInfos[i].connectedApIdx >= 0) {
            totalSpecialClients++;
            totalImportance += CalculateClientImportance(i);
        }
    }

    int maxAllowedPerAp = (totalSpecialClients + kNumAps - 1) / kNumAps;
    cout << "特殊客户端总数: " << totalSpecialClients << ", 每AP理想分配: " << maxAllowedPerAp << endl;

    // 对于特殊客户端过多的AP，按重要性优先迁移
    for (size_t apIdx = 0; apIdx < apInfos.size(); ++apIdx) {
        auto& cliList = apSpecialClients[apIdx];
        if ((int)cliList.size() > maxAllowedPerAp) {
            cout << "\nAP" << apIdx << " 特殊客户端过多(" << cliList.size() << ">" << maxAllowedPerAp << ")，开始迁移..." << endl;

            // 按重要性排序，重要性低的优先迁移
            sort(cliList.begin(), cliList.end(), [](int a, int b) {
                return CalculateClientImportance(a) < CalculateClientImportance(b);
            });

            //需要迁移多余的特殊客户端
            for (size_t k = maxAllowedPerAp; k < cliList.size(); ++k) {
                int cliIdx = cliList[k];

                //选择目标AP（特殊客户端最少且能容纳的AP）
                size_t minAp = apIdx;
                int minCount = cliList.size();
                for (size_t j = 0; j < apInfos.size(); ++j) {
                    if (j == apIdx) continue;
                    if ((int)apSpecialClients[j].size() < minCount &&
                        (int)apSpecialClients[j].size() < maxAllowedPerAp) {
                        minCount = apSpecialClients[j].size();
                        minAp = j;
                    }
                }

                if (minAp == apIdx) {
                    cout << "警告：无法为" << GetClientTypeString(clientInfos[cliIdx].clientType)
                         << "客户端 " << clientInfos[cliIdx].mac << " 找到合适的目标AP" << endl;
                    continue;
                }
                //实现"一换一"机制：先找到目标AP上最适合迁移回来的普通客户端
                int swapClientIdx = -1;
                double worstScore = 1e9;

                // 统计当前各AP的连接数（用于评分计算）
                vector<int> currentApStaCount(apInfos.size(), 0);
                for (auto& client : clientInfos) {
                    if (client.connectedApIdx >= 0) {
                        currentApStaCount[client.connectedApIdx]++;
                    }
                }

                //在目标AP上找一个普通客户端来交换
                for (auto& client : clientInfos) {
                    if (client.connectedApIdx == (int)minAp && client.clientType == NORMAL) {
                        //计算这个客户端在当前AP的评分（评分越低越适合迁移走）
                        double score = CalculateClientScore(&client - &clientInfos[0], minAp, currentApStaCount);
                        if (score < worstScore) {
                            worstScore = score;
                            swapClientIdx = &client - &clientInfos[0];
                        }
                    }
                }

                int currentAp = clientInfos[cliIdx].connectedApIdx;

                //执行"一换一"迁移
                if (swapClientIdx >= 0 && currentAp >= 0) {
                    cout << "\n=== 执行一换一迁移 ===" << endl;

                    //1. 特殊客户端从当前AP迁移到目标AP
                    string reason = GetClientTypeString(clientInfos[cliIdx].clientType) + "负载均衡-一换一";
                    ApplyBlacklistWithDisassociation(currentAp, clientInfos[cliIdx].mac, reason);

                    //在除目标AP外的所有AP上加黑名单
                    for (size_t j = 0; j < apInfos.size(); ++j) {
                        if (j != minAp) {
                            apInfos[j].blacklist.insert(clientInfos[cliIdx].mac);
                        } else {
                            apInfos[j].blacklist.erase(clientInfos[cliIdx].mac);
                        }
                    }
                    clientInfos[cliIdx].connectedApIdx = minAp;

                    //2. 普通客户端从目标AP迁移到原AP
                    ApplyBlacklistWithDisassociation(minAp, clientInfos[swapClientIdx].mac, reason + "回迁");

                    //在除原AP外的所有AP上加黑名单
                    for (size_t j = 0; j < apInfos.size(); ++j) {
                        if (j != (size_t)currentAp) {
                            apInfos[j].blacklist.insert(clientInfos[swapClientIdx].mac);
                        } else {
                            apInfos[j].blacklist.erase(clientInfos[swapClientIdx].mac);
                        }
                    }
                    clientInfos[swapClientIdx].connectedApIdx = currentAp;

                    cout << "一换一完成：" << GetClientTypeString(clientInfos[cliIdx].clientType)
                         << "客户端 " << clientInfos[cliIdx].mac
                         << " (AP" << currentAp << "->AP" << minAp << ") <-> 普通客户端 "
                         << clientInfos[swapClientIdx].mac << " (AP" << minAp << "->AP" << currentAp << ")" << endl;

                } else {
                    //如果找不到合适的交换对象，执行普通迁移
                    cout << "未找到合适的交换客户端，执行普通迁移" << endl;
                    if (currentAp >= 0) {
                        string reason = GetClientTypeString(clientInfos[cliIdx].clientType) + "负载均衡";
                        ApplyBlacklistWithDisassociation(currentAp, clientInfos[cliIdx].mac, reason);
                    }

                    for (size_t j = 0; j < apInfos.size(); ++j) {
                        if (j != minAp) {
                            apInfos[j].blacklist.insert(clientInfos[cliIdx].mac);
                        } else {
                            apInfos[j].blacklist.erase(clientInfos[cliIdx].mac);
                        }
                    }
                    clientInfos[cliIdx].connectedApIdx = minAp;
                }

                apSpecialClients[minAp].push_back(cliIdx);
                SimulationDelay(300);
            }
        }
    }

    cout << "=== 统一特殊客户端负载均衡完成 ===" << endl;
}

//最终强制负载均衡：确保所有AP的连接数尽可能均匀
void FinalLoadBalance() {
    cout << "\n=== 开始最终强制负载均衡 ===" << endl;

    // 统计当前各AP连接数
    vector<int> apStaCount(apInfos.size(), 0);
    for (auto& client : clientInfos) {
        if (client.connectedApIdx >= 0) {
            apStaCount[client.connectedApIdx]++;
        }
    }

    // 计算理想分配
    int totalClients = 0;
    for (int count : apStaCount) totalClients += count;
    int targetLoad = totalClients / kNumAps;
    int remainder = totalClients % kNumAps;

    cout << "当前分布: ";
    for (size_t i = 0; i < apStaCount.size(); ++i) {
        cout << "AP" << i << "(" << apStaCount[i] << ") ";
    }
    cout << endl;
    cout << "目标分配: 每AP " << targetLoad << "个，前" << remainder << "个AP各多1个" << endl;

    bool needBalance = true;
    int iterations = 0;
    const int maxIterations = 20;

    while (needBalance && iterations < maxIterations) {
        needBalance = false;
        iterations++;

        // 找出负载最高和最低的AP
        int maxAp = 0, minAp = 0;
        for (size_t i = 1; i < apStaCount.size(); ++i) {
            if (apStaCount[i] > apStaCount[maxAp]) maxAp = i;
            if (apStaCount[i] < apStaCount[minAp]) minAp = i;
        }

        int maxTarget = targetLoad + (maxAp < remainder ? 1 : 0);
        int minTarget = targetLoad + (minAp < remainder ? 1 : 0);

        // 如果最高AP超载且最低AP未满，进行迁移
        if (apStaCount[maxAp] > maxTarget && apStaCount[minAp] < minTarget) {
            // 找一个普通客户端从maxAp迁移到minAp
            for (auto& client : clientInfos) {
                if (client.connectedApIdx == maxAp && client.clientType == NORMAL) {
                    // 执行迁移
                    ApplyBlacklistWithDisassociation(maxAp, client.mac, "最终负载均衡");

                    // 设置黑名单：只能连接到minAp
                    for (size_t j = 0; j < apInfos.size(); ++j) {
                        if (j != (size_t)minAp) {
                            apInfos[j].blacklist.insert(client.mac);
                        } else {
                            apInfos[j].blacklist.erase(client.mac);
                        }
                    }

                    client.connectedApIdx = minAp;
                    apStaCount[maxAp]--;
                    apStaCount[minAp]++;
                    needBalance = true;

                    cout << "迁移：普通客户端 " << client.mac << " 从AP" << maxAp << "->AP" << minAp << endl;
                    break;
                }
            }
        }

        // 检查是否还需要继续均衡
        if (!needBalance) {
            for (size_t i = 0; i < apStaCount.size(); ++i) {
                int target = targetLoad + (i < (size_t)remainder ? 1 : 0);
                if (abs(apStaCount[i] - target) > 1) {
                    needBalance = true;
                    break;
                }
            }
        }
    }

    cout << "最终分布: ";
    for (size_t i = 0; i < apStaCount.size(); ++i) {
        cout << "AP" << i << "(" << apStaCount[i] << ") ";
    }
    cout << endl;
    cout << "=== 最终强制负载均衡完成 ===" << endl;
}

//迟到学生仿真：随机选择学生进入教室并连接到最优AP
void SimulateLateStudents() {
    cout << "\n==== 迟到学生进入教室仿真 ====" << endl;
    SimulationDelay(800);
    cout << "模拟 " << kNumLateStudents << " 个迟到学生（上课迟到/上厕所回来）进入教室..." << endl;
    SimulationDelay(500);

    //随机选择迟到的学生
    set<int> lateStudentIndices;
    vector<int> availableStudents;

    //找出当前没有客户端连接的学生（模拟他们暂时离开了教室）
    for (int i = 0; i < kNumStudents; ++i) {
        bool hasConnectedClient = false;
        for (auto& client : clientInfos) {
            if (client.studentIdx == i && client.connectedApIdx >= 0) {
                hasConnectedClient = true;
                break;
            }
        }
        if (!hasConnectedClient) {
            availableStudents.push_back(i);
        }
    }

    //如果没有足够的"离开"学生，就从所有学生中随机选择
    if (availableStudents.size() < kNumLateStudents) {
        availableStudents.clear();
        for (int i = 0; i < kNumStudents; ++i) {
            availableStudents.push_back(i);
        }
    }

    //随机选择迟到学生
    random_shuffle(availableStudents.begin(), availableStudents.end());
    for (int i = 0; i < min(kNumLateStudents, (int)availableStudents.size()); ++i) {
        lateStudentIndices.insert(availableStudents[i]);
    }

    cout << "选中的迟到学生索引: ";
    for (int idx : lateStudentIndices) {
        cout << idx << " ";
    }
    cout << endl;
    SimulationDelay(400);

    //为每个迟到学生的客户端重新连接到最优AP
    vector<int> apStaCount(apInfos.size(), 0);

    //先统计当前各AP的连接数
    for (auto& client : clientInfos) {
        if (client.connectedApIdx >= 0) {
            apStaCount[client.connectedApIdx]++;
        }
    }

    //计算目标负载均衡
    int targetLoad = kNumClients / kNumAps;
    size_t remainder = kNumClients % kNumAps;

    int reconnectedClients = 0;
    for (auto& client : clientInfos) {
        if (lateStudentIndices.count(client.studentIdx)) {
            //这是迟到学生的客户端，需要重新连接
            int bestAp = -1;
            double bestScore = -1e9;

            Vector clientPos = studentInfos[client.studentIdx].pos;

            for (size_t j = 0; j < apInfos.size(); ++j) {
                if (apStaCount[j] >= apInfos[j].maxSta) continue; //跳过已满
                if (apInfos[j].blacklist.count(client.mac)) continue; //跳过黑名单

                //计算距离因子
                double distance = Distance(apInfos[j].pos, clientPos);
                double distanceFactor = 1.0 / (1.0 + distance * 0.1);

                //计算信号强度因子
                double rssi = CalcRssi(apInfos[j], client);
                double rssiNormalized = (rssi + 100.0) / 50.0;
                rssiNormalized = max(0.0, min(1.0, rssiNormalized));

                //计算负载均衡因子（迟到学生也要考虑负载均衡）
                int currentTarget = targetLoad + (j < remainder ? 1 : 0);
                double loadFactor = 1.0 - (double)apStaCount[j] / currentTarget;
                loadFactor = max(0.1, loadFactor);

                // 如果AP负载已经超过目标，给予重度惩罚
                if (apStaCount[j] > currentTarget) {
                    loadFactor = 0.05; // 严重惩罚超载AP
                }

                //根据学生位置调整权重：按半径距离判断前后排
                double positionBias = 1.0;
                double studentRadius = sqrt(clientPos.x * clientPos.x + clientPos.y * clientPos.y);

                if (studentRadius < 11.0) { //前排学生（半径小于11米，环扇形较窄区域）
                    if (j < 2) positionBias = 1.4; //前排AP（AP0,1）加权
                    else positionBias = 0.6;        //后排AP（AP2,3,4）减权
                } else { //后排学生（半径大于等于11米，环扇形较宽区域）
                    if (j >= 2) positionBias = 1.4; //后排AP（AP2,3,4）加权
                    else positionBias = 0.6;         //前排AP（AP0,1）减权
                }

                //综合评分：降低距离权重，提高负载均衡权重
                double score = (0.2 * distanceFactor + 0.25 * rssiNormalized + 0.45 * loadFactor) * positionBias;

                if (score > bestScore) {
                    bestScore = score;
                    bestAp = j;
                }
            }

            if (bestAp != -1) {
                client.connectedApIdx = bestAp;
                apStaCount[bestAp]++;
                reconnectedClients++;
                double studentRadius = sqrt(clientPos.x * clientPos.x + clientPos.y * clientPos.y);
                cout << "  迟到学生" << client.studentIdx << "(半径=" << studentRadius << "m, 高度=" << clientPos.z
                     << "m)的客户端 " << client.mac << " 连接到 AP" << bestAp << " (评分: " << bestScore << ")" << endl;
                SimulationDelay(150);
            } else {
                cout << "警告：迟到学生" << client.studentIdx << "的客户端 " << client.mac
                     << " 无法找到可用AP" << endl;
            }
        }
    }

    cout << "\n 成功为 " << reconnectedClients << " 个迟到学生的客户端重新分配AP" << endl;
    SimulationDelay(500);
    cout << "==== 迟到学生仿真完成 ====\n" << endl;
    SimulationDelay(1000);
}




int main(int argc, char *argv[]) {
    srand(time(NULL));
    
   
    g_lossModel = CreateObject<LogDistancePropagationLossModel>();
    g_lossModel->SetAttribute("ReferenceDistance", DoubleValue(1.0));
    g_lossModel->SetAttribute("ReferenceLoss", DoubleValue(46.6777));
    g_lossModel->SetAttribute("Exponent", DoubleValue(3.0));
    
    //1.初始化AP和学生、客户端位置
    InitApPositions();
    InitStudentPositions();
    InitClientInfos();

    //2.创建节点
    NodeContainer apNodes, clientNodes;
    apNodes.Create(kNumAps);
    clientNodes.Create(kNumClients);

    //3.配置WiFi
    WifiHelper wifi;
    wifi.SetStandard(WIFI_STANDARD_80211ac);
    YansWifiPhyHelper phy;
    YansWifiChannelHelper channel = YansWifiChannelHelper::Default();
    phy.SetChannel(channel.Create());
    WifiMacHelper mac;
    Ssid ssid = Ssid("classroom-wifi");

    //AP设备
    NetDeviceContainer apDevices;
    for (int i = 0; i < kNumAps; ++i) {
        mac.SetType("ns3::ApWifiMac", "Ssid", SsidValue(ssid));
        phy.Set("TxPowerStart", DoubleValue(apInfos[i].txPowerDbm));
        phy.Set("TxPowerEnd", DoubleValue(apInfos[i].txPowerDbm));
        NetDeviceContainer dev = wifi.Install(phy, mac, apNodes.Get(i));
        apInfos[i].node = apNodes.Get(i);
        apInfos[i].device = DynamicCast<WifiNetDevice>(dev.Get(0));
        apDevices.Add(dev);
    }

    //客户端设备
    NetDeviceContainer clientDevices;
    for (int i = 0; i < kNumClients; ++i) {
        mac.SetType("ns3::StaWifiMac", "Ssid", SsidValue(ssid));
        NetDeviceContainer dev = wifi.Install(phy, mac, clientNodes.Get(i));
        clientInfos[i].node = clientNodes.Get(i);
        clientInfos[i].device = DynamicCast<WifiNetDevice>(dev.Get(0));
        clientDevices.Add(dev);
    }

    //4.配置移动模型
    MobilityHelper mobility;
    Ptr<ListPositionAllocator> apPosAlloc = CreateObject<ListPositionAllocator>();
    for (auto& ap : apInfos) apPosAlloc->Add(ap.pos);
    mobility.SetPositionAllocator(apPosAlloc);
    mobility.SetMobilityModel("ns3::ConstantPositionMobilityModel");
    mobility.Install(apNodes);

    Ptr<ListPositionAllocator> clientPosAlloc = CreateObject<ListPositionAllocator>();
    for (auto& client : clientInfos) clientPosAlloc->Add(studentInfos[client.studentIdx].pos);
    mobility.SetPositionAllocator(clientPosAlloc);
    mobility.SetMobilityModel("ns3::ConstantPositionMobilityModel");
    mobility.Install(clientNodes);

    //5.配置IP
    InternetStackHelper stack;
    stack.Install(apNodes);
    stack.Install(clientNodes);
    Ipv4AddressHelper address;
    address.SetBase("***********", "***********");
    Ipv4InterfaceContainer apIfs = address.Assign(apDevices);
    Ipv4InterfaceContainer clientIfs = address.Assign(clientDevices);

    //6.记录客户端的MAC/IP
    for (int i = 0; i < kNumClients; ++i) {
        clientInfos[i].mac = Mac48Address::ConvertFrom(clientInfos[i].device->GetAddress());
        clientInfos[i].ip = clientIfs.GetAddress(i);
    }

    //7.初始化特殊客户端（高流量、高延迟、双属性）
    const int kNumHighDelayClients = 50; //高延迟客户端数目

    cout << "\n=== 初始化特殊客户端类型 ===" << endl;

    // 先随机选择高流量客户端
    set<int> highTrafficIdx;
    while (highTrafficIdx.size() < kNumHighTrafficClients) {
        int idx = rand() % kNumClients;
        highTrafficIdx.insert(idx);
    }

    // 再随机选择高延迟客户端（可能与高流量重叠）
    set<int> highDelayIdx;
    while (highDelayIdx.size() < kNumHighDelayClients) {
        int idx = rand() % kNumClients;
        highDelayIdx.insert(idx);
    }

    // 统计各类型客户端数量
    int normalCount = 0, highTrafficOnlyCount = 0, highDelayOnlyCount = 0, dualCount = 0;

    // 设置客户端属性和类型
    for (int i = 0; i < kNumClients; ++i) {
        bool isHighTraffic = highTrafficIdx.count(i) > 0;
        bool isHighDelay = highDelayIdx.count(i) > 0;

        clientInfos[i].isHighTraffic = isHighTraffic;
        clientInfos[i].isHighDelay = isHighDelay;

        if (isHighTraffic && isHighDelay) {
            clientInfos[i].clientType = HIGH_TRAFFIC_AND_DELAY;
            dualCount++;
        } else if (isHighTraffic) {
            clientInfos[i].clientType = HIGH_TRAFFIC_ONLY;
            highTrafficOnlyCount++;
        } else if (isHighDelay) {
            clientInfos[i].clientType = HIGH_DELAY_ONLY;
            highDelayOnlyCount++;
        } else {
            clientInfos[i].clientType = NORMAL;
            normalCount++;
        }
    }

    cout << "客户端类型分布:" << endl;
    cout << "  普通客户端: " << normalCount << " 个" << endl;
    cout << "  仅高流量客户端: " << highTrafficOnlyCount << " 个" << endl;
    cout << "  仅高延迟客户端: " << highDelayOnlyCount << " 个" << endl;
    cout << "  双属性客户端(高流量+高延迟): " << dualCount << " 个" << endl;
    cout << "  总计: " << (normalCount + highTrafficOnlyCount + highDelayOnlyCount + dualCount) << " 个" << endl;
    cout << "=== 特殊客户端类型初始化完成 ===\n" << endl;

    //8.初始连接分配（前排2个AP分配65%，后排3个AP分配35%）
    // AP0为门口AP，连接数要多于AP1
    vector<int> apStaCount(apInfos.size(), 0);
    vector<int> clientIdx(kNumClients);
    iota(clientIdx.begin(), clientIdx.end(), 0);
    random_shuffle(clientIdx.begin(), clientIdx.end());

    int numFront = round(kNumClients * 0.65); //前排65%
    int numBack = kNumClients - numFront;     //后排35%

    //前排分配：AP0(门口的)分配更多，AP1分配较少
    //AP0分配前排的60%，AP1分配前排的40%
    int ap0Num = round(numFront * 0.6); //门口AP0分配更多
    int ap1Num = numFront - ap0Num;     //AP1分配剩余的

    cout << "\n=== 初始连接分配计划 ===" << endl;
    SimulationDelay(600);
    cout << "前排总数: " << numFront << " (65%)" << endl;
    SimulationDelay(200);
    cout << "  AP0(门口): " << ap0Num << " 个客户端" << endl;
    SimulationDelay(200);
    cout << "  AP1(前排): " << ap1Num << " 个客户端" << endl;
    SimulationDelay(200);
    cout << "后排总数: " << numBack << " (35%)" << endl;
    SimulationDelay(400);

    //后排3个AP平均分配
    vector<int> backApNum(3, numBack / 3); //基础分配
    for (int i = 0; i < numBack % 3; ++i) { //余数分配
        backApNum[i]++;
    }

    //分配到AP
    int idx = 0;

    //AP0 (门口AP)
    int cnt = 0;
    while (cnt < ap0Num && apStaCount[0] < apInfos[0].maxSta && idx < kNumClients) {
        int cli = clientIdx[idx++];
        clientInfos[cli].connectedApIdx = 0;
        apStaCount[0]++;
        cnt++;
    }
    cout << "AP0实际分配: " << cnt << " 个客户端 (计划: " << ap0Num << ")" << endl;
    SimulationDelay(300);

    //AP1(前排另一个AP)
    cnt = 0;
    while (cnt < ap1Num && apStaCount[1] < apInfos[1].maxSta && idx < kNumClients) {
        int cli = clientIdx[idx++];
        clientInfos[cli].connectedApIdx = 1;
        apStaCount[1]++;
        cnt++;
    }
    cout << "AP1实际分配: " << cnt << " 个客户端 (计划: " << ap1Num << ")" << endl;
    SimulationDelay(300);

    //后排AP (AP2, AP3, AP4)
    for (int ap = 0; ap < 3; ++ap) {
        int apIdx = ap + 2;
        cnt = 0;
        while (cnt < backApNum[ap] && apStaCount[apIdx] < apInfos[apIdx].maxSta && idx < kNumClients) {
            int cli = clientIdx[idx++];
            clientInfos[cli].connectedApIdx = apIdx;
            apStaCount[apIdx]++;
            cnt++;
        }
    }
    //剩余未分配的终端（如果有），按信号强度分配
    for (; idx < kNumClients; ++idx) {
        int cli = clientIdx[idx];
        //记录每个AP的信号强度
        vector<pair<double, int>> rssiApIdx;
        for (size_t j = 0; j < apInfos.size(); ++j) {
            if (apStaCount[j] < apInfos[j].maxSta) {
                double rssi = CalcRssi(apInfos[j], clientInfos[cli]);
                rssiApIdx.push_back({rssi, (int)j});
            }
        }
        if (!rssiApIdx.empty()) {
            sort(rssiApIdx.rbegin(), rssiApIdx.rend());
            int chosenAp = rssiApIdx[0].second;
            clientInfos[cli].connectedApIdx = chosenAp;
            apStaCount[chosenAp]++;
        } else {
            clientInfos[cli].connectedApIdx = -1; //无可用AP
        }
    }

    //9.输出初始状态
    PrintApStaStatus("初始分配");
    PrintSpecialClients();

    //10.负载均衡（解除关联+重新分配）
    DisassociateAndRebalance();
    PrintApStaStatus("解除关联后");

    //10.5.迟到学生仿真（上课迟到和上厕所回来的学生）
    SimulateLateStudents();
    PrintApStaStatus("迟到学生连接后");

    //11.统一特殊客户端负载均衡（处理高流量、高延迟、双属性客户端）
    SpecialClientBalance();
    PrintApStaStatus("特殊客户端均衡后");
    PrintBlacklist();

    //12.输出最终负载均衡结果（保持仿真随机性，不进行强制均衡）
    PrintApStaStatus("最终负载均衡结果");

    //13.应用流量模型
    uint16_t port = 9;
    ApplicationContainer serverApps, clientApps;
    for (int i = 0; i < kNumHighTrafficClients; ++i) {
        int idx = *next(highTrafficIdx.begin(), i);
        UdpServerHelper server(port + i);
        serverApps.Add(server.Install(clientInfos[idx].node));
    }
    serverApps.Start(Seconds(0.1));
    serverApps.Stop(Seconds(1.0));

    for (int i = 0; i < kNumHighTrafficClients; ++i) {
        int idx = *next(highTrafficIdx.begin(), i);
        UdpClientHelper client(clientInfos[idx].ip, port + i);
        client.SetAttribute("MaxPackets", UintegerValue(10000));
        client.SetAttribute("Interval", TimeValue(MilliSeconds(1)));
        client.SetAttribute("PacketSize", UintegerValue(1024));
        clientApps.Add(client.Install(apInfos[0].node)); // 随便选一个AP发流量
    }
    clientApps.Start(Seconds(0.2));
    clientApps.Stop(Seconds(1.0));

    //14.启用FlowMonitor
    FlowMonitorHelper flowHelper;
    Ptr<FlowMonitor> flowMonitor = flowHelper.InstallAll();

    Simulator::Stop(Seconds(1.1));
    Simulator::Run();



    Simulator::Destroy();
    return 0;
}
