//NS-3网络仿真器WiFi负载均衡仿真程序
//实现环扇形阶梯教室中的多AP WiFi负载均衡算法
//包含基于信号强度的负载均衡、特殊客户端处理、黑名单机制等功能

//NS-3核心模块头文件
#include "ns3/core-module.h"
#include "ns3/network-module.h"
#include "ns3/mobility-module.h"
#include "ns3/internet-module.h"
#include "ns3/wifi-module.h"
#include "ns3/applications-module.h"
#include "ns3/ipv4-address-helper.h"
#include "ns3/ssid.h"
#include "ns3/flow-monitor-module.h"

//标准C++库头文件
#include <vector>
#include <cmath>
#include <map>
#include <set>
#include <algorithm>
#include <iostream>
#include <thread>
#include <chrono>

using namespace ns3;
using namespace std;

//定义NS-3日志组件
NS_LOG_COMPONENT_DEFINE ("ApNotUse");
//数学常量π的定义
#define PI 3.14159265358979323846

//仿真参数配置

//环扇形阶梯教室物理布局参数
const double kClassroomRadiusMin = 6.0;    //最内圈半径(米)-讲台区域
const double kClassroomRadiusMax = 20.0;   //最外圈半径(米)-增加到20米以容纳更多排
const double kClassroomAngle = 120.0;      //扇形角度(度)-120度扇形教室
const double kRowSpacing = 1.2;            //阶梯行间距(米)-每排之间的距离
const double kSeatSpacing = 0.8;           //座位间距(米)-同排座位之间的距离
const int kRowsCount = 12;                 //阶梯行数-增加到12排容纳更多学生
const double kStepHeight = 0.3;            //每级台阶高度(米)-阶梯教室高度差

//网络设备数量配置
const int kNumStudents = 200;              //学生总数-每个学生2个设备
const int kNumClients = 400;               //客户端设备总数-200学生×2设备
const int kNumAps = 5;                     //AP总数-5个接入点
const int kNumHighPowerAps = 3;            //高功率AP数量-前排3个
const int kNumLowPowerAps = 2;             //低功率AP数量-后排2个

//WiFi功率配置
const double kLowPowerDbm = 12.0;          //低功率AP发射功率-后排AP
const double kHighPowerDbm = 16.0;         //高功率AP发射功率-前排AP

//AP容量限制配置
const int kMaxStaPerLowAp = 100;           //初始后排低功率AP最大连接数
const int kMaxStaPerHighAp = 150;          //初始前排高功率AP最大连接数

//特殊客户端配置
const int kNumHighTrafficClients = 70;     //高流量客户端数量（延迟在后面代码中设置）
const int kNumLateStudents = 5;            //迟到学生数量-每个学生有两个终端

//全局信号传播损耗模型指针
Ptr<PropagationLossModel> g_lossModel;
//数据结构定义

//AP信息的结构体
struct ApInfo {
    Vector pos;                        //AP的3D坐标位置(x,y,z)
    double txPowerDbm;                 //AP发射功率(dBm)
    Ptr<Node> node;                    //NS-3节点指针
    Ptr<WifiNetDevice> device;         //WiFi网络设备指针
    set<Mac48Address> blacklist;       //黑名单-存储被禁止连接的客户端MAC地址
    int maxSta;                        //最大允许连接的客户端数量
};

//学生位置信息结构体
struct StudentInfo {
    Vector pos;                        //学生的3D坐标位置(x,y,z)
};

//类型枚举
enum ClientType {
    NORMAL = 0,                        //普通客户端-标准业务需求
    HIGH_TRAFFIC_ONLY = 1,             //仅高流量客户端-需要大带宽
    HIGH_DELAY_ONLY = 2,               //仅高延迟敏感客户端-需要低延迟
    HIGH_TRAFFIC_AND_DELAY = 3         //双属性客户端-既需要大带宽又需要低延迟
};

//客户端信息结构体
struct ClientInfo {
    int studentIdx;                    //所属学生的索引号
    Ptr<Node> node;                    //NS-3节点指针
    Ptr<WifiNetDevice> device;         //WiFi网络设备指针
    Mac48Address mac;                  //MAC地址
    Ipv4Address ip;                    //IP地址
    bool isHighTraffic;                //是否为高流量客户端标志
    bool isHighDelay;                  //是否为高延迟敏感客户端标志
    ClientType clientType;             //客户端类型枚举值
    int connectedApIdx;                //当前连接的AP索引(-1表示未连接)
};

//全局数据容器
vector<ApInfo> apInfos;                   //存储所有AP信息的向量
vector<StudentInfo> studentInfos;         //存储所有学生位置信息的向量
vector<ClientInfo> clientInfos;           //存储所有客户端信息的向量
map<Mac48Address, int> clientToAp;        //客户端MAC地址到AP索引的映射表

//工具函数

void SimulationDelay(int milliseconds) {
    std::this_thread::sleep_for(std::chrono::milliseconds(milliseconds));
}

//发送802.11解除关联帧函数
//功能：AP向指定客户端发送解除关联帧，断开WiFi连接
//参数：apIdx-AP索引，staMac-客户端MAC地址，reason-断开原因描述
void SendDisassociationFrame(int apIdx, const Mac48Address& staMac, const string& reason) {
    //检查AP索引有效性
    if (apIdx < 0 || apIdx >= (int)apInfos.size()) {
        cout << "错误：AP索引无效 " << apIdx << endl;
        return;
    }

    //获取AP的WiFi设备指针
    Ptr<WifiNetDevice> apDevice = apInfos[apIdx].device;
    if (!apDevice) {
        cout << "错误：AP" << apIdx << " 设备为空" << endl;
        return;
    }

    //获取AP的MAC层对象
    Ptr<WifiMac> apMac = apDevice->GetMac();
    Ptr<ApWifiMac> apWifiMac = DynamicCast<ApWifiMac>(apMac);

    //检查MAC层对象有效性
    if (!apWifiMac) {
        cout << "错误：无法获取AP" << apIdx << " 的MAC层" << endl;
        return;
    }

    cout << "AP" << apIdx << " 向客户端 " << staMac << " 发送解除关联帧 (原因: " << reason << ")" << endl;

    //获取AP的MAC地址
    Mac48Address apMacAddr = Mac48Address::ConvertFrom(apDevice->GetAddress());

    //创建802.11解除关联帧头部
    WifiMacHeader hdr;
    hdr.SetType(WIFI_MAC_MGT_DISASSOCIATION);  //设置帧类型为解除关联管理帧
    hdr.SetAddr1(staMac);                      //目标地址-客户端MAC地址
    hdr.SetAddr2(apMacAddr);                   //源地址-AP的MAC地址
    hdr.SetAddr3(apMacAddr);                   //BSSID-基本服务集标识符
    hdr.SetDsNotFrom();                        //设置DS位-不来自分布式系统
    hdr.SetDsNotTo();                          //设置DS位-不发往分布式系统

    //创建解除关联帧数据包
    Ptr<Packet> packet = Create<Packet>(2);    //创建2字节数据包存储原因码
    uint16_t reasonCode = 3;                   //解除关联原因码：3=STA主动离开
    packet->AddHeader(hdr);                    //添加MAC头部到数据包

    cout << "解除关联帧已准备发送 (原因码: " << reasonCode << ")" << endl;
    SimulationDelay(100);                      //模拟帧传输延迟100毫秒
}

//发送802.11解除认证帧函数（没用到）
//功能：AP向指定客户端发送解除认证帧，强制断开WiFi连接
//参数：apIdx-AP索引，staMac-客户端MAC地址，reason-断开原因描述
//注：解除认证比解除关联更严格，需要重新进行完整的认证过程
void SendDeauthenticationFrame(int apIdx, const Mac48Address& staMac, const string& reason) {
    //检查AP索引有效性
    if (apIdx < 0 || apIdx >= (int)apInfos.size()) {
        cout << "错误：AP索引无效 " << apIdx << endl;
        return;
    }

    //获取AP的WiFi设备指针
    Ptr<WifiNetDevice> apDevice = apInfos[apIdx].device;
    if (!apDevice) {
        cout << "错误：AP" << apIdx << " 设备为空" << endl;
        return;
    }

    //获取AP的MAC层对象
    Ptr<WifiMac> apMac = apDevice->GetMac();
    Ptr<ApWifiMac> apWifiMac = DynamicCast<ApWifiMac>(apMac);

    //检查MAC层对象有效性
    if (!apWifiMac) {
        cout << "错误：无法获取AP" << apIdx << " 的MAC层" << endl;
        return;
    }

    cout << "AP" << apIdx << " 向客户端 " << staMac << " 发送解除认证帧 (原因: " << reason << ")" << endl;

    //获取AP的MAC地址
    Mac48Address apMacAddr = Mac48Address::ConvertFrom(apDevice->GetAddress());

    //创建802.11解除认证帧头部
    WifiMacHeader hdr;
    hdr.SetType(WIFI_MAC_MGT_DEAUTHENTICATION);    //设置帧类型为解除认证管理帧
    hdr.SetAddr1(staMac);                          //目标地址-客户端MAC地址
    hdr.SetAddr2(apMacAddr);                       //源地址-AP的MAC地址
    hdr.SetAddr3(apMacAddr);                       //BSSID-基本服务集标识符
    hdr.SetDsNotFrom();                            //设置DS位-不来自分布式系统
    hdr.SetDsNotTo();                              //设置DS位-不发往分布式系统

    //创建解除认证帧数据包
    Ptr<Packet> packet = Create<Packet>(2);        //创建2字节数据包存储原因码
    uint16_t reasonCode = 2;                       //解除认证原因码：2=之前的认证无效
    packet->AddHeader(hdr);                        //添加MAC头部到数据包

    cout << "解除认证帧已准备发送 (原因码: " << reasonCode << ")" << endl;
    SimulationDelay(100);                          //模拟帧传输延迟100毫秒
}

//实现802.11黑名单机制函数
void ApplyBlacklistWithDisassociation(int apIdx, const Mac48Address& staMac, const string& reason) {
    cout << "\n 执行802.11黑名单机制:" << endl;
    SimulationDelay(200);

    //第一步：将客户端MAC地址加入AP黑名单(防止立即重连)
    apInfos[apIdx].blacklist.insert(staMac);
    cout << "客户端 " << staMac << " 已加入AP" << apIdx << " 黑名单" << endl;
    SimulationDelay(100);                  

    //第二步：发送802.11解除关联帧通知客户端
    SendDisassociationFrame(apIdx, staMac, reason);

    //第三步：断开客户端连接状态
    for (auto& client : clientInfos) {
        if (client.mac == staMac && client.connectedApIdx == apIdx) {
            client.connectedApIdx = -1;            //设置为未连接状态
            cout << "客户端 " << staMac << " 已从AP" << apIdx << " 断开连接" << endl;
            break;
        }
    }

    SimulationDelay(300);
    cout << "黑名单机制执行完成（顺序：黑名单->解除关联帧->断开连接）\n" << endl;
}

//====================AP布局初始化函数====================
//功能：初始化环扇形阶梯教室中的AP位置布局
//布局策略：前排2个高功率AP覆盖主要区域，后排3个低功率AP覆盖边缘区域
void InitApPositions() {
    //环扇形教室AP布局设计：前排高功率+后排低功率的分层覆盖策略

    //前排AP配置：AP0和AP1位于教室前排两侧
    double frontAngle1 = -25.0 * PI / 180.0;      //左前方角度-25度
    double frontAngle2 = 25.0 * PI / 180.0;       //右前方角度+25度
    double frontRadius = 7.0;                     //前排半径7米-靠近讲台
    double frontHeight = 2.5;                     //前排安装高度2.5米

    //创建AP0(左前方高功率AP)
    apInfos.push_back({Vector(frontRadius * sin(frontAngle1), frontRadius * cos(frontAngle1), frontHeight),
                      kHighPowerDbm, nullptr, nullptr, {}, kMaxStaPerHighAp});
    //创建AP1(右前方高功率AP)
    apInfos.push_back({Vector(frontRadius * sin(frontAngle2), frontRadius * cos(frontAngle2), frontHeight),
                      kHighPowerDbm, nullptr, nullptr, {}, kMaxStaPerHighAp});

    //后排AP配置：AP2、AP3、AP4位于教室后排三个位置
    double backAngle1 = -50.0 * PI / 180.0;       //左后方角度-50度
    double backAngle2 = 0.0 * PI / 180.0;         //正后方角度0度
    double backAngle3 = 50.0 * PI / 180.0;        //右后方角度+50度
    double backRadius = 16.0;                     //后排半径16米-远离讲台
    double backHeight = 4.0;                      //后排安装高度4米-更高位置

    //创建AP2(左后方低功率AP)
    apInfos.push_back({Vector(backRadius * sin(backAngle1), backRadius * cos(backAngle1), backHeight),
                      kLowPowerDbm, nullptr, nullptr, {}, kMaxStaPerLowAp});
    //创建AP3(正后方低功率AP)
    apInfos.push_back({Vector(backRadius * sin(backAngle2), backRadius * cos(backAngle2), backHeight),
                      kLowPowerDbm, nullptr, nullptr, {}, kMaxStaPerLowAp});
    //创建AP4(右后方低功率AP)
    apInfos.push_back({Vector(backRadius * sin(backAngle3), backRadius * cos(backAngle3), backHeight),
                      kLowPowerDbm, nullptr, nullptr, {}, kMaxStaPerLowAp});

    //输出AP布局信息到控制台
    cout << "\n=== AP布局初始化 ===" << endl;
    SimulationDelay(500);
    cout << "环扇形阶梯教室AP布局:" << endl;
    SimulationDelay(300);

    //显示前排高功率AP信息
    cout << "前排AP（高功率）:" << endl;
    for (int i = 0; i < 2; ++i) {
        cout << "  AP" << i << ": 位置(" << apInfos[i].pos.x << ", " << apInfos[i].pos.y
             << ", " << apInfos[i].pos.z << "), 功率=" << apInfos[i].txPowerDbm << "dBm (高功率)" << endl;
        SimulationDelay(200);
    }

    //显示后排低功率AP信息
    cout << "后排AP（低功率）:" << endl;
    for (int i = 2; i < 5; ++i) {
        cout << "  AP" << i << ": 位置(" << apInfos[i].pos.x << ", " << apInfos[i].pos.y
             << ", " << apInfos[i].pos.z << "), 功率=" << apInfos[i].txPowerDbm << "dBm (低功率)" << endl;
        SimulationDelay(200);
    }
    cout << "=== AP布局完成 ===\n" << endl;
    SimulationDelay(800);
}

//学生位置初始化函数
//功能：在环扇形阶梯教室中初始化200个学生的座位位置
//布局策略：前2排空置，从第3排开始按阶梯递增方式安排学生
void InitStudentPositions() {
    cout << "\n=== 学生位置初始化 ===" << endl;
    SimulationDelay(500);
    cout << "正在初始化环扇形阶梯教室学生位置..." << endl;
    SimulationDelay(300);

    //第一排半径设置-比教室内半径稍大(讲台与第一排之间的距离)
    const double kFirstRowRadius = kClassroomRadiusMin + 0.8;  //比内半径大0.8米

    //预定义每排的学生人数-确保递增分布且总数为200
    //前2排不坐人(靠近讲台),从第3排开始逐排递增
    vector<int> seatsPerRow = {0, 0, 15, 18, 21, 25, 28, 31, 34, 37, 40, 43}; //12排配置

    //验证总座位数是否符合要求
    int totalSeats = 0;
    for (int seats : seatsPerRow) {
        totalSeats += seats;
    }
    cout << "计划总座位数: " << totalSeats << " (需要200个学生)" << endl;

    //如果总数不等于200，动态调整后排座位数
    if (totalSeats != kNumStudents) {
        int diff = kNumStudents - totalSeats;      //计算差值
        cout << "调整座位数差值: " << diff << endl;

        //从后往前调整座位数以达到精确的200人
        for (int i = seatsPerRow.size() - 1; i >= 2 && diff != 0; --i) {
            if (diff > 0) {                       //需要增加座位
                int add = min(diff, 5);            //每排最多增加5个座位
                seatsPerRow[i] += add;
                diff -= add;
            } else {                               //需要减少座位
                int sub = min(-diff, 3);           //每排最多减少3个座位
                seatsPerRow[i] = max(0, seatsPerRow[i] - sub);
                diff += sub;
            }
        }
    }

    //计算每排之间的半径递增步长
    double radiusStep = (kClassroomRadiusMax - kFirstRowRadius) / (kRowsCount - 1);
    int studentsPlaced = 0;                        //已安排学生计数器

    //逐排安排学生座位
    for (int row = 0; row < kRowsCount && studentsPlaced < kNumStudents; ++row) {
        //计算当前排的半径
        double radius;
        if (row == 0) {
            radius = kFirstRowRadius;              //第一排使用特定半径
        } else {
            radius = kFirstRowRadius + (row - 1) * radiusStep;  //后续排按步长递增
        }
        double rowHeight = row * kStepHeight;      //计算阶梯高度

        int seatsInRow = seatsPerRow[row];         //获取当前排的座位数

        //处理空排(前2排不坐学生)
        if (seatsInRow == 0) {
            cout << "  第" << (row + 1) << "排: 空排（前2排不坐学生）" << endl;
            SimulationDelay(100);
            continue;
        }

        //计算当前排座位的角度分布
        double angleStep = kClassroomAngle / (seatsInRow + 1);  //角度间距(+1为两端留空隙)
        double startAngle = -kClassroomAngle / 2.0;             //起始角度(扇形中心对称)

        cout << "  第" << (row + 1) << "排: 半径=" << radius << "m, 高度=" << rowHeight
             << "m, 座位数=" << seatsInRow << endl;
        SimulationDelay(100);

        //在当前排安排每个座位的学生
        for (int seat = 0; seat < seatsInRow && studentsPlaced < kNumStudents; ++seat) {
            double angle = startAngle + (seat + 1) * angleStep;  //计算座位角度
            double angleRad = angle * PI / 180.0;                //转换为弧度

            //极坐标转直角坐标计算学生位置
            double x = radius * sin(angleRad);     //X坐标(左右方向)
            double y = radius * cos(angleRad);     //Y坐标(前后方向)
            double z = rowHeight;                  //Z坐标(高度方向)

            //添加小量随机偏移模拟真实座位的微小差异
            double offsetX = ((double)rand() / RAND_MAX - 0.5) * 0.3;  //±15cm水平偏移
            double offsetY = ((double)rand() / RAND_MAX - 0.5) * 0.3;  //±15cm前后偏移
            double offsetZ = ((double)rand() / RAND_MAX - 0.5) * 0.1;  //±5cm高度偏移

            x += offsetX;
            y += offsetY;
            z += offsetZ;

            //将学生位置添加到全局容器
            studentInfos.push_back({Vector(x, y, z)});
            studentsPlaced++;
        }
    }

    //输出学生位置初始化结果
    cout << "\n 成功初始化 " << studentsPlaced << " 个学生位置（环扇形阶梯布局）" << endl;
    SimulationDelay(300);
    cout << "教室参数: 内半径=" << kClassroomRadiusMin << "m, 第一排半径=" << kFirstRowRadius
         << "m, 外半径=" << kClassroomRadiusMax << "m, 扇形角度=" << kClassroomAngle << "度, 阶梯数=" << kRowsCount << endl;
    cout << "前2排空置，学生从第3排开始就座，每排人数递增" << endl;
    cout << "=== 学生位置初始化完成 ===\n" << endl;
    SimulationDelay(800);
}

//客户端信息初始化函数
//功能：为每个学生分配2-3个客户端设备(手机、平板、笔记本等)
//分配策略：每个学生至少2个设备，前100个学生额外分配1个设备
void InitClientInfos() {
    //计算客户端分配策略
    int baseClients = kNumClients / kNumStudents;  //基础分配：每学生2个设备
    int extra = kNumClients % kNumStudents;        //额外设备数：200个
    int idx = 0;                                   //客户端索引计数器

    //为每个学生分配客户端设备
    for (int i = 0; i < kNumStudents; ++i) {
        //前100个学生分配3个设备，后100个学生分配2个设备
        int num = baseClients + (i < extra ? 1 : 0);
        for (int j = 0; j < num; ++j) {
            //创建客户端信息对象并添加到全局容器
            clientInfos.push_back({i, nullptr, nullptr, Mac48Address(), Ipv4Address(), false, false, NORMAL, -1});
            idx++;
        }
    }
}

//信号计算相关函数

//返回：两点间的直线距离(米)
double Distance(const Vector& a, const Vector& b) {
    return sqrt(pow(a.x - b.x, 2) + pow(a.y - b.y, 2));
}

//计算AP到客户端的接收信号强度函数
//功能：使用NS-3信道传播模型计算RSSI值
//参数：ap-AP信息，client-客户端信息
//返回：接收信号强度(dBm)
double CalcRssi(const ApInfo& ap, const ClientInfo& client) {
    Vector apPos = ap.pos;                         //获取AP位置
    Vector staPos = studentInfos[client.studentIdx].pos;  //获取客户端位置

    //创建移动性模型对象用于信号计算
    Ptr<MobilityModel> apMob = CreateObject<ConstantPositionMobilityModel>();
    apMob->SetPosition(apPos);
    Ptr<MobilityModel> staMob = CreateObject<ConstantPositionMobilityModel>();
    staMob->SetPosition(staPos);

    //使用全局传播损耗模型计算接收功率
    double rxPowerDbm = g_lossModel->CalcRxPower(ap.txPowerDbm, apMob, staMob);
    return rxPowerDbm;
}

//AP选择算法函数

//选择信号最强的AP函数(基础版本)
//功能：为客户端选择信号强度最好且不在黑名单中的AP
//参数：client-客户端信息
//返回：最佳AP的索引，-1表示无可用AP
int SelectBestAp(const ClientInfo& client) {
    double bestRssi = -1e9;                        //初始化最佳信号强度
    int bestIdx = -1;                              //初始化最佳AP索引

    //遍历所有AP寻找最佳选择
    for (size_t i = 0; i < apInfos.size(); ++i) {
        if (apInfos[i].blacklist.count(client.mac)) continue;  //跳过黑名单AP
        double rssi = CalcRssi(apInfos[i], client);            //计算信号强度
        if (rssi > bestRssi) {
            bestRssi = rssi;
            bestIdx = i;
        }
    }
    return bestIdx;
}

//选择信号最强的AP函数(容量限制版本)
//功能：为客户端选择信号强度最好且不在黑名单中且未满载的AP
//参数：client-客户端信息，apStaCount-各AP当前连接数统计
//返回：最佳AP的索引，-1表示无可用AP
int SelectBestAp(const ClientInfo& client, const vector<int>& apStaCount) {
    double bestRssi = -1e9;                        //初始化最佳信号强度
    int bestIdx = -1;                              //初始化最佳AP索引

    //遍历所有AP寻找最佳选择
    for (size_t i = 0; i < apInfos.size(); ++i) {
        if (apInfos[i].blacklist.count(client.mac)) continue;  //跳过黑名单AP
        if (apStaCount[i] >= apInfos[i].maxSta) continue;      //跳过已满载AP
        double rssi = CalcRssi(apInfos[i], client);            //计算信号强度
        if (rssi > bestRssi) {
            bestRssi = rssi;
            bestIdx = i;
        }
    }
    return bestIdx;
}

//====================负载均衡评分算法====================

//计算客户端在指定AP上的综合评分函数
//功能：综合考虑距离、信号强度、负载均衡、位置偏好等因素计算评分
//参数：clientIdx-客户端索引，apIdx-AP索引，apStaCount-各AP当前连接数
//返回：综合评分(0-1之间)，-1e9表示不可连接
double CalculateClientScore(int clientIdx, int apIdx, const vector<int>& apStaCount) {
    //检查AP索引有效性
    if (apIdx < 0 || apIdx >= (int)apInfos.size()) return -1e9;
    //检查是否在黑名单中
    if (apInfos[apIdx].blacklist.count(clientInfos[clientIdx].mac)) return -1e9;
    //检查AP容量限制
    if (apStaCount[apIdx] >= apInfos[apIdx].maxSta) return -1e9;

    Vector clientPos = studentInfos[clientInfos[clientIdx].studentIdx].pos;

    //计算距离因子(距离越近评分越高)
    double distance = Distance(apInfos[apIdx].pos, clientPos);
    double distanceFactor = 1.0 / (1.0 + distance * 0.1);

    //计算信号强度因子(信号越强评分越高)
    double rssi = CalcRssi(apInfos[apIdx], clientInfos[clientIdx]);
    double rssiNormalized = (rssi + 100.0) / 50.0;        //归一化到0-1范围
    rssiNormalized = max(0.0, min(1.0, rssiNormalized));

    //计算负载均衡因子(当前负载越低评分越高)
    int targetLoad = kNumClients / kNumAps;                //目标平均负载
    size_t remainder = kNumClients % kNumAps;              //余数分配
    int currentTarget = targetLoad + (apIdx < (int)remainder ? 1 : 0);  //当前AP目标负载
    double loadFactor = 1.0 - (double)apStaCount[apIdx] / currentTarget;
    loadFactor = max(0.1, loadFactor);                     //保证最小值0.1

    //根据学生位置计算位置偏好因子
    double positionBias = 1.0;
    double studentRadius = sqrt(clientPos.x * clientPos.x + clientPos.y * clientPos.y);

    if (studentRadius < 11.0) {                            //前排学生(半径<11米)
        if (apIdx < 2) positionBias = 1.4;                 //前排AP加权40%
        else positionBias = 0.6;                           //后排AP减权40%
    } else {                                               //后排学生(半径>=11米)
        if (apIdx >= 2) positionBias = 1.4;                //后排AP加权40%
        else positionBias = 0.6;                           //前排AP减权40%
    }

    //综合评分计算：距离30%+信号强度30%+负载均衡30%，再乘以位置偏好10%
    double score = (0.3 * distanceFactor + 0.3 * rssiNormalized + 0.3 * loadFactor) * positionBias;

    return score;
}

//计算客户端重要性评分函数
//功能：根据客户端类型计算其在负载均衡中的优先级权重
//参数：clientIdx-客户端索引
//返回：重要性评分(1.0-3.0)，数值越高优先级越高
double CalculateClientImportance(int clientIdx) {
    const ClientInfo& client = clientInfos[clientIdx];
    double importance = 1.0;                               //基础重要性评分

    //根据客户端类型分配不同的重要性权重
    switch (client.clientType) {
        case NORMAL:
            importance = 1.0;                              //普通客户端-基础优先级
            break;
        case HIGH_TRAFFIC_ONLY:
            importance = 2.0;                              //高流量客户端-较高优先级
            break;
        case HIGH_DELAY_ONLY:
            importance = 1.8;                              //高延迟敏感客户端-中等优先级
            break;
        case HIGH_TRAFFIC_AND_DELAY:
            importance = 3.0;                              //双属性客户端-最高优先级
            break;
    }

    return importance;
}

//====================辅助函数====================

//获取客户端类型的中文字符串描述函数
//参数：type-客户端类型枚举值
//返回：对应的中文描述字符串
string GetClientTypeString(ClientType type) {
    switch (type) {
        case NORMAL: return "普通";
        case HIGH_TRAFFIC_ONLY: return "高流量";
        case HIGH_DELAY_ONLY: return "高延迟";
        case HIGH_TRAFFIC_AND_DELAY: return "高流量+高延迟";
        default: return "未知";
    }
}

//====================状态输出函数====================

//输出各AP连接状态函数
//功能：统计并显示每个AP当前的客户端连接数量和位置信息
//参数：stage-当前阶段的描述字符串
void PrintApStaStatus(string stage) {
    cout << "\n==== " << stage << " ====" << endl;
    SimulationDelay(600);

    //遍历所有AP统计连接数
    for (size_t i = 0; i < apInfos.size(); ++i) {
        int cnt = 0;
        //统计连接到当前AP的客户端数量
        for (auto& client : clientInfos) {
            if ((size_t)client.connectedApIdx == i) ++cnt;
        }
        cout << "AP" << i << " (" << apInfos[i].pos.x << "," << apInfos[i].pos.y << ") 连接数: " << cnt << endl;
        SimulationDelay(300);
    }
    cout << "========================\n" << endl;
    SimulationDelay(1000);
}

//输出特殊客户端信息函数
//功能：按类型分组显示所有非普通类型的客户端详细信息
void PrintSpecialClients() {
    cout << "\n=== 特殊客户端详细信息 ===" << endl;

    //按客户端类型分组
    map<ClientType, vector<int>> typeGroups;
    for (size_t i = 0; i < clientInfos.size(); ++i) {
        if (clientInfos[i].clientType != NORMAL) {
            typeGroups[clientInfos[i].clientType].push_back(i);
        }
    }

    //按类型输出特殊客户端信息
    for (auto& pair : typeGroups) {
        cout << GetClientTypeString(pair.first) << "客户端 (" << pair.second.size() << "个):" << endl;
        for (int idx : pair.second) {
            cout << "  " << clientInfos[idx].ip << " / " << clientInfos[idx].mac
                 << " (重要性: " << CalculateClientImportance(idx) << ")" << endl;
        }
        cout << endl;
    }
}

//输出黑名单状态信息函数
//功能：显示各AP的黑名单中包含的客户端MAC地址列表
void PrintBlacklist() {
    cout << "\n=== 802.11黑名单状态 ===" << endl;
    SimulationDelay(400);

    bool hasBlacklist = false;                             //标记是否存在黑名单

    //遍历所有AP检查黑名单状态
    for (size_t i = 0; i < apInfos.size(); ++i) {
        if (!apInfos[i].blacklist.empty()) {
            hasBlacklist = true;
            cout << "AP" << i << " 黑名单 (" << apInfos[i].blacklist.size() << " 个客户端): ";
            //输出黑名单中的所有MAC地址
            for (auto& mac : apInfos[i].blacklist) {
                cout << mac << " ";
            }
            cout << endl;
            SimulationDelay(200);
        }
    }

    //如果没有任何黑名单则显示提示信息
    if (!hasBlacklist) {
        cout << "当前没有客户端在黑名单中" << endl;
    }

    cout << "========================\n" << endl;
    SimulationDelay(600);
}

//====================第一层负载均衡函数====================
//功能：基于信号强度和容量限制的负载均衡算法
//策略：先调整AP容量限制，然后解除所有连接，最后基于信号强度重新分配
void DisassociateAndRebalance() {
    cout << "\n=== 开始第一层负载均衡（基于信号强度+容量限制） ===" << endl;

    //步骤0：调整AP最大连接数实现负载均衡兜底机制
    cout << "调整AP最大连接数以确保负载均衡..." << endl;
    SimulationDelay(300);

    //设置新的AP容量限制(目标：每AP约80个客户端)
    int newMaxForHighPowerAp = 85;                         //前排高功率AP容量
    int newMaxForLowPowerAp = 80;                          //后排低功率AP容量

    cout << "原始AP容量配置:" << endl;
    for (size_t i = 0; i < apInfos.size(); ++i) {
        cout << "  AP" << i << ": " << apInfos[i].maxSta << " -> ";
        if (i < 2) {                                       //前排高功率AP(AP0,AP1)
            apInfos[i].maxSta = newMaxForHighPowerAp;
        } else {                                           //后排低功率AP(AP2,AP3,AP4)
            apInfos[i].maxSta = newMaxForLowPowerAp;
        }
        cout << apInfos[i].maxSta << endl;
        SimulationDelay(100);
    }

    //验证调整后的总容量是否足够
    int totalCapacity = 2 * newMaxForHighPowerAp + 3 * newMaxForLowPowerAp;
    cout << "调整后总容量: " << totalCapacity << " (需要容纳400个客户端)" << endl;
    if (totalCapacity < kNumClients) {
        cout << "警告：总容量不足！需要进一步调整" << endl;
    }
    SimulationDelay(500);

    //步骤1：解除所有AP上所有客户端的关联连接
    cout << "解除所有客户端连接..." << endl;
    for (size_t i = 0; i < apInfos.size(); ++i) {
        for (size_t j = 0; j < clientInfos.size(); ++j) {
            if ((size_t)clientInfos[j].connectedApIdx == i) {
                cout << "AP" << i << " 解除关联 客户端 " << clientInfos[j].mac << endl;
                clientInfos[j].connectedApIdx = -1;        //重置连接状态
            }
        }
    }

    //步骤2：基于信号强度重新分配客户端(受新容量限制约束)
    cout << "基于信号强度重新分配（受新容量限制）..." << endl;
    vector<int> apStaCount(apInfos.size(), 0);             //各AP连接数统计

    //为每个客户端选择最佳AP进行重新连接
    for (size_t i = 0; i < clientInfos.size(); ++i) {
        int bestAp = SelectBestAp(clientInfos[i], apStaCount);  //选择信号最强且未满载的AP

        if (bestAp != -1) {
            clientInfos[i].connectedApIdx = bestAp;        //建立连接
            apStaCount[bestAp]++;                          //更新连接计数
            cout << "客户端 " << clientInfos[i].mac << " 重新连接到 AP" << bestAp
                 << " (基于信号强度+AP容量限制)" << endl;
        } else {
            cout << "警告：客户端 " << clientInfos[i].mac << " 无法找到可用AP（所有AP已满或在黑名单中）" << endl;
        }
    }

}

//====================特殊客户端负载均衡函数====================
//功能：统一处理高流量、高延迟、双属性等特殊客户端的负载均衡
//策略：清理黑名单，统计分布，基于重要性和综合评分进行重新分配
void SpecialClientBalance() {
    cout << "\n=== 开始统一特殊客户端负载均衡 ===" << endl;

    //步骤1：清理所有特殊客户端的历史黑名单记录
    cout << "清理特殊客户端的历史黑名单记录..." << endl;
    for (size_t i = 0; i < clientInfos.size(); ++i) {
        if (clientInfos[i].clientType != NORMAL) {
            for (size_t j = 0; j < apInfos.size(); ++j) {
                if (apInfos[j].blacklist.erase(clientInfos[i].mac)) {
                    cout << "清理：" << GetClientTypeString(clientInfos[i].clientType)
                         << "客户端 " << clientInfos[i].mac << " 从AP" << j << " 黑名单移除" << endl;
                }
            }
        }
    }

    //步骤2：统计各AP上的特殊客户端分布情况
    vector<vector<int>> apSpecialClients(apInfos.size());  //各AP的特殊客户端列表
    map<ClientType, int> typeCount;                        //各类型客户端总数统计

    for (size_t i = 0; i < clientInfos.size(); ++i) {
        if (clientInfos[i].clientType != NORMAL && clientInfos[i].connectedApIdx >= 0) {
            apSpecialClients[clientInfos[i].connectedApIdx].push_back(i);
            typeCount[clientInfos[i].clientType]++;
        }
    }

    //输出当前特殊客户端分布统计
    cout << "当前特殊客户端分布:" << endl;
    for (auto& pair : typeCount) {
        cout << "  " << GetClientTypeString(pair.first) << ": " << pair.second << " 个" << endl;
    }
    //步骤3：计算理想的特殊客户端分布(按重要性加权)
    int totalSpecialClients = 0;                           //特殊客户端总数
    double totalImportance = 0.0;                          //总重要性权重

    for (size_t i = 0; i < clientInfos.size(); ++i) {
        if (clientInfos[i].clientType != NORMAL && clientInfos[i].connectedApIdx >= 0) {
            totalSpecialClients++;
            totalImportance += CalculateClientImportance(i);
        }
    }

    //计算每个AP理想的特殊客户端数量上限
    int maxAllowedPerAp = (totalSpecialClients + kNumAps - 1) / kNumAps;
    cout << "特殊客户端总数: " << totalSpecialClients << ", 每AP理想分配: " << maxAllowedPerAp << endl;

    //步骤4：对特殊客户端过多的AP进行负载均衡迁移
    for (size_t apIdx = 0; apIdx < apInfos.size(); ++apIdx) {
        auto& cliList = apSpecialClients[apIdx];
        if ((int)cliList.size() > maxAllowedPerAp) {
            cout << "\nAP" << apIdx << " 特殊客户端过多(" << cliList.size() << ">" << maxAllowedPerAp << ")，开始迁移..." << endl;

            //按重要性排序-重要性低的客户端优先迁移
            sort(cliList.begin(), cliList.end(), [](int a, int b) {
                return CalculateClientImportance(a) < CalculateClientImportance(b);
            });

            //迁移超出限制的特殊客户端
            for (size_t k = maxAllowedPerAp; k < cliList.size(); ++k) {
                int cliIdx = cliList[k];

                //选择目标AP(特殊客户端数量最少且未达上限的AP)
                size_t minAp = apIdx;
                int minCount = cliList.size();
                for (size_t j = 0; j < apInfos.size(); ++j) {
                    if (j == apIdx) continue;
                    if ((int)apSpecialClients[j].size() < minCount &&
                        (int)apSpecialClients[j].size() < maxAllowedPerAp) {
                        minCount = apSpecialClients[j].size();
                        minAp = j;
                    }
                }

                //检查是否找到合适的目标AP
                if (minAp == apIdx) {
                    cout << "警告：无法为" << GetClientTypeString(clientInfos[cliIdx].clientType)
                         << "客户端 " << clientInfos[cliIdx].mac << " 找到合适的目标AP" << endl;
                    continue;
                }
                //实现"一换一"交换机制：寻找目标AP上最适合迁移的普通客户端
                int swapClientIdx = -1;                        //待交换的普通客户端索引
                double worstScore = 1e9;                       //最低评分(越低越适合迁移)

                //统计当前各AP的连接数(用于评分计算)
                vector<int> currentApStaCount(apInfos.size(), 0);
                for (auto& client : clientInfos) {
                    if (client.connectedApIdx >= 0) {
                        currentApStaCount[client.connectedApIdx]++;
                    }
                }

                //在目标AP上寻找最适合交换的普通客户端
                for (auto& client : clientInfos) {
                    if (client.connectedApIdx == (int)minAp && client.clientType == NORMAL) {
                        //计算该客户端在当前AP的适配评分(评分越低越适合迁移走)
                        double score = CalculateClientScore(&client - &clientInfos[0], minAp, currentApStaCount);
                        if (score < worstScore) {
                            worstScore = score;
                            swapClientIdx = &client - &clientInfos[0];
                        }
                    }
                }

                int currentAp = clientInfos[cliIdx].connectedApIdx;  //当前特殊客户端所在AP

                //执行"一换一"客户端交换迁移
                if (swapClientIdx >= 0 && currentAp >= 0) {
                    cout << "\n=== 执行一换一迁移 ===" << endl;

                    //步骤1：特殊客户端从当前AP迁移到目标AP
                    string reason = GetClientTypeString(clientInfos[cliIdx].clientType) + "负载均衡-一换一";
                    ApplyBlacklistWithDisassociation(currentAp, clientInfos[cliIdx].mac, reason);

                    //设置黑名单：除目标AP外的所有AP都拒绝该特殊客户端
                    for (size_t j = 0; j < apInfos.size(); ++j) {
                        if (j != minAp) {
                            apInfos[j].blacklist.insert(clientInfos[cliIdx].mac);
                        } else {
                            apInfos[j].blacklist.erase(clientInfos[cliIdx].mac);
                        }
                    }
                    clientInfos[cliIdx].connectedApIdx = minAp;     //更新连接状态

                    //步骤2：普通客户端从目标AP迁移回原AP
                    ApplyBlacklistWithDisassociation(minAp, clientInfos[swapClientIdx].mac, reason + "回迁");

                    //设置黑名单：除原AP外的所有AP都将该客户端加入黑名单
                    for (size_t j = 0; j < apInfos.size(); ++j) {
                        if (j != (size_t)currentAp) {
                            apInfos[j].blacklist.insert(clientInfos[swapClientIdx].mac);
                        } else {
                            apInfos[j].blacklist.erase(clientInfos[swapClientIdx].mac);
                        }
                    }
                    clientInfos[swapClientIdx].connectedApIdx = currentAp;  //更新连接状态

                    //输出交换完成信息
                    cout << "一换一完成：" << GetClientTypeString(clientInfos[cliIdx].clientType)
                         << "客户端 " << clientInfos[cliIdx].mac
                         << " (AP" << currentAp << "->AP" << minAp << ") <-> 普通客户端 "
                         << clientInfos[swapClientIdx].mac << " (AP" << minAp << "->AP" << currentAp << ")" << endl;

                } else {
                    //如果找不到合适的交换对象，执行普通单向迁移
                    cout << "未找到合适的交换客户端，执行普通迁移" << endl;
                    if (currentAp >= 0) {
                        string reason = GetClientTypeString(clientInfos[cliIdx].clientType) + "负载均衡";
                        ApplyBlacklistWithDisassociation(currentAp, clientInfos[cliIdx].mac, reason);
                    }

                    //设置黑名单：除目标AP外的所有AP都拒绝该客户端
                    for (size_t j = 0; j < apInfos.size(); ++j) {
                        if (j != minAp) {
                            apInfos[j].blacklist.insert(clientInfos[cliIdx].mac);
                        } else {
                            apInfos[j].blacklist.erase(clientInfos[cliIdx].mac);
                        }
                    }
                    clientInfos[cliIdx].connectedApIdx = minAp;    //更新连接状态
                }

                //更新目标AP的特殊客户端列表
                apSpecialClients[minAp].push_back(cliIdx);
                SimulationDelay(300);
            }
        }
    }

    cout << "=== 统一特殊客户端负载均衡完成 ===" << endl;
}


//====================迟到学生仿真函数====================
void SimulateLateStudents() {
    cout << "\n==== 迟到学生进入教室仿真 ====" << endl;
    SimulationDelay(800);
    cout << "模拟 " << kNumLateStudents << " 个迟到学生（上课迟到/上厕所回来）进入教室..." << endl;
    SimulationDelay(500);

    //步骤1：选择迟到的学生
    set<int> lateStudentIndices;                           //迟到学生索引集合
    vector<int> availableStudents;                         //可选学生列表

    //寻找当前没有客户端连接的学生(模拟他们暂时离开了教室)
    for (int i = 0; i < kNumStudents; ++i) {
        bool hasConnectedClient = false;
        for (auto& client : clientInfos) {
            if (client.studentIdx == i && client.connectedApIdx >= 0) {
                hasConnectedClient = true;
                break;
            }
        }
        if (!hasConnectedClient) {
            availableStudents.push_back(i);
        }
    }

    //如果没有足够的"离开"学生，就从所有学生中随机选择
    if (availableStudents.size() < kNumLateStudents) {
        availableStudents.clear();
        for (int i = 0; i < kNumStudents; ++i) {
            availableStudents.push_back(i);
        }
    }

    //随机选择指定数量的迟到学生
    random_shuffle(availableStudents.begin(), availableStudents.end());
    for (int i = 0; i < min(kNumLateStudents, (int)availableStudents.size()); ++i) {
        lateStudentIndices.insert(availableStudents[i]);
    }

    cout << "选中的迟到学生索引: ";
    for (int idx : lateStudentIndices) {
        cout << idx << " ";
    }
    cout << endl;
    SimulationDelay(400);

    //步骤2：为迟到学生的客户端重新连接到最优AP
    vector<int> apStaCount(apInfos.size(), 0);             //各AP当前连接数统计

    //统计当前各AP的连接数
    for (auto& client : clientInfos) {
        if (client.connectedApIdx >= 0) {
            apStaCount[client.connectedApIdx]++;
        }
    }

    //计算目标负载均衡参数
    int targetLoad = kNumClients / kNumAps;                //基础目标负载
    size_t remainder = kNumClients % kNumAps;              //余数分配

    //步骤3：为迟到学生的客户端执行重新连接
    int reconnectedClients = 0;                            //重连客户端计数
    for (auto& client : clientInfos) {
        if (lateStudentIndices.count(client.studentIdx)) {
            //这是迟到学生的客户端，需要重新连接
            int bestAp = -1;                               //最佳AP索引
            double bestScore = -1e9;                       //最佳评分

            Vector clientPos = studentInfos[client.studentIdx].pos;  //客户端位置

            //遍历所有AP寻找最佳连接选择
            for (size_t j = 0; j < apInfos.size(); ++j) {
                if (apStaCount[j] >= apInfos[j].maxSta) continue;     //跳过已满载AP
                if (apInfos[j].blacklist.count(client.mac)) continue; //跳过黑名单AP

                //计算距离因子(距离越近评分越高)
                double distance = Distance(apInfos[j].pos, clientPos);
                double distanceFactor = 1.0 / (1.0 + distance * 0.1);

                //计算信号强度因子(信号越强评分越高)
                double rssi = CalcRssi(apInfos[j], client);
                double rssiNormalized = (rssi + 100.0) / 50.0;       
                rssiNormalized = max(0.0, min(1.0, rssiNormalized));

                //计算负载均衡因子(迟到学生也要考虑负载均衡)
                int currentTarget = targetLoad + (j < remainder ? 1 : 0);
                double loadFactor = 1.0 - (double)apStaCount[j] / currentTarget;
                loadFactor = max(0.1, loadFactor);

                //如果AP负载已经超过目标，给予惩罚
                if (apStaCount[j] > currentTarget) {
                    loadFactor = 0.05;                                
                }

                //根据学生位置计算位置偏好因子
                double positionBias = 1.0;
                double studentRadius = sqrt(clientPos.x * clientPos.x + clientPos.y * clientPos.y);

                if (studentRadius < 11.0) {                            //前排学生(半径<11米)
                    if (j < 2) positionBias = 1.4;                     //前排AP(AP0,1)加权
                    else positionBias = 0.6;                           //后排AP(AP2,3,4)减权
                } else {                                               //后排学生(半径>=11米)
                    if (j >= 2) positionBias = 1.4;                    //后排AP(AP2,3,4)加权
                    else positionBias = 0.6;                           //前排AP(AP0,1)减权
                }

                //综合评分计算：降低距离权重，提高负载均衡权重
                double score = (0.2 * distanceFactor + 0.25 * rssiNormalized + 0.45 * loadFactor) * positionBias;

                if (score > bestScore) {
                    bestScore = score;
                    bestAp = j;
                }
            }

            //执行客户端连接到最佳AP
            if (bestAp != -1) {
                client.connectedApIdx = bestAp;               //更新连接状态
                apStaCount[bestAp]++;                         //更新AP负载统计
                reconnectedClients++;                         //更新重连计数
                double studentRadius = sqrt(clientPos.x * clientPos.x + clientPos.y * clientPos.y);
                cout << "  迟到学生" << client.studentIdx << "(半径=" << studentRadius << "m, 高度=" << clientPos.z
                     << "m)的客户端 " << client.mac << " 连接到 AP" << bestAp << " (评分: " << bestScore << ")" << endl;
                SimulationDelay(150);
            } else {
                cout << "警告：迟到学生" << client.studentIdx << "的客户端 " << client.mac
                     << " 无法找到可用AP" << endl;
            }
        }
    }

    //输出迟到学生仿真结果
    cout << "\n 成功为 " << reconnectedClients << " 个迟到学生的客户端重新分配AP" << endl;
    SimulationDelay(500);
    cout << "==== 迟到学生仿真完成 ====\n" << endl;
    SimulationDelay(1000);
}




//====================主函数====================
//功能：WiFi负载均衡仿真程序的主入口函数
//流程：初始化环境->创建网络拓扑->配置WiFi->分配客户端类型->执行负载均衡->运行仿真
int main(int argc, char *argv[]) {
    srand(time(NULL));                                

    //初始化NS-3信道传播损耗模型
    g_lossModel = CreateObject<LogDistancePropagationLossModel>();
    g_lossModel->SetAttribute("ReferenceDistance", DoubleValue(1.0));      //参考距离1米
    g_lossModel->SetAttribute("ReferenceLoss", DoubleValue(46.6777));      //参考损耗
    g_lossModel->SetAttribute("Exponent", DoubleValue(3.0));               //路径损耗指数

    //步骤1：初始化仿真环境(AP位置、学生位置、客户端信息)
    InitApPositions();
    InitStudentPositions();
    InitClientInfos();

    //步骤2：创建NS-3网络节点
    NodeContainer apNodes, clientNodes;                    //AP节点和客户端节点容器
    apNodes.Create(kNumAps);                               //创建5个AP节点
    clientNodes.Create(kNumClients);                       //创建400个客户端节点

    //步骤3：配置WiFi网络参数
    WifiHelper wifi;
    wifi.SetStandard(WIFI_STANDARD_80211ac);               //使用802.11ac标准
    YansWifiPhyHelper phy;                                 //物理层配置助手
    YansWifiChannelHelper channel = YansWifiChannelHelper::Default();
    phy.SetChannel(channel.Create());                      //创建信道
    WifiMacHelper mac;                                     //MAC层配置助手
    Ssid ssid = Ssid("classroom-wifi");                    //设置SSID

    //配置AP设备
    NetDeviceContainer apDevices;                          //AP网络设备容器
    for (int i = 0; i < kNumAps; ++i) {
        mac.SetType("ns3::ApWifiMac", "Ssid", SsidValue(ssid));  //设置为AP模式
        phy.Set("TxPowerStart", DoubleValue(apInfos[i].txPowerDbm));  //设置发射功率
        phy.Set("TxPowerEnd", DoubleValue(apInfos[i].txPowerDbm));
        NetDeviceContainer dev = wifi.Install(phy, mac, apNodes.Get(i));  //安装WiFi设备
        apInfos[i].node = apNodes.Get(i);                      //关联节点对象
        apInfos[i].device = DynamicCast<WifiNetDevice>(dev.Get(0));  //关联网络设备
        apDevices.Add(dev);                                //添加到设备容器
    }

    //配置客户端设备
    NetDeviceContainer clientDevices;                      //客户端网络设备容器
    for (int i = 0; i < kNumClients; ++i) {
        mac.SetType("ns3::StaWifiMac", "Ssid", SsidValue(ssid));  //设置为STA模式
        NetDeviceContainer dev = wifi.Install(phy, mac, clientNodes.Get(i));  //安装WiFi设备
        clientInfos[i].node = clientNodes.Get(i);          //关联节点对象
        clientInfos[i].device = DynamicCast<WifiNetDevice>(dev.Get(0));  //关联网络设备
        clientDevices.Add(dev);                            //添加到设备容器
    }

    //步骤4：配置移动性模型(固定位置)
    MobilityHelper mobility;

    //配置AP位置
    Ptr<ListPositionAllocator> apPosAlloc = CreateObject<ListPositionAllocator>();
    for (auto& ap : apInfos) apPosAlloc->Add(ap.pos);      //添加AP位置
    mobility.SetPositionAllocator(apPosAlloc);
    mobility.SetMobilityModel("ns3::ConstantPositionMobilityModel");  //固定位置模型
    mobility.Install(apNodes);                             //应用到AP节点

    //配置客户端位置
    Ptr<ListPositionAllocator> clientPosAlloc = CreateObject<ListPositionAllocator>();
    for (auto& client : clientInfos) clientPosAlloc->Add(studentInfos[client.studentIdx].pos);  //添加客户端位置
    mobility.SetPositionAllocator(clientPosAlloc);
    mobility.SetMobilityModel("ns3::ConstantPositionMobilityModel");  //固定位置模型
    mobility.Install(clientNodes);                         //应用到客户端节点

    //步骤5：配置IP协议栈和地址分配
    InternetStackHelper stack;
    stack.Install(apNodes);                                //为AP节点安装协议栈
    stack.Install(clientNodes);                            //为客户端节点安装协议栈
    Ipv4AddressHelper address;
    address.SetBase("***********", "***********");        //设置IP地址段
    Ipv4InterfaceContainer apIfs = address.Assign(apDevices);        //分配AP IP地址
    Ipv4InterfaceContainer clientIfs = address.Assign(clientDevices); //分配客户端IP地址

    //步骤6：记录客户端的MAC地址和IP地址
    for (int i = 0; i < kNumClients; ++i) {
        clientInfos[i].mac = Mac48Address::ConvertFrom(clientInfos[i].device->GetAddress());  //获取MAC地址
        clientInfos[i].ip = clientIfs.GetAddress(i);       //获取IP地址
    }

    //步骤7：初始化特殊客户端类型(高流量、高延迟、双属性)
    const int kNumHighDelayClients = 50;                   //高延迟客户端数目

    cout << "\n=== 初始化特殊客户端类型 ===" << endl;

    //随机选择高流量客户端
    set<int> highTrafficIdx;
    while (highTrafficIdx.size() < kNumHighTrafficClients) {
        int idx = rand() % kNumClients;
        highTrafficIdx.insert(idx);
    }

    //随机选择高延迟客户端(可能与高流量重叠形成双属性客户端)
    set<int> highDelayIdx;
    while (highDelayIdx.size() < kNumHighDelayClients) {
        int idx = rand() % kNumClients;
        highDelayIdx.insert(idx);
    }

    //统计各类型客户端数量
    int normalCount = 0, highTrafficOnlyCount = 0, highDelayOnlyCount = 0, dualCount = 0;

    //设置客户端属性和类型
    for (int i = 0; i < kNumClients; ++i) {
        bool isHighTraffic = highTrafficIdx.count(i) > 0;  //是否为高流量客户端
        bool isHighDelay = highDelayIdx.count(i) > 0;      //是否为高延迟客户端

        clientInfos[i].isHighTraffic = isHighTraffic;      //设置高流量属性
        clientInfos[i].isHighDelay = isHighDelay;          //设置高延迟属性

        //根据属性组合确定客户端类型
        if (isHighTraffic && isHighDelay) {
            clientInfos[i].clientType = HIGH_TRAFFIC_AND_DELAY;  //双属性客户端
            dualCount++;
        } else if (isHighTraffic) {
            clientInfos[i].clientType = HIGH_TRAFFIC_ONLY;       //仅高流量客户端
            highTrafficOnlyCount++;
        } else if (isHighDelay) {
            clientInfos[i].clientType = HIGH_DELAY_ONLY;         //仅高延迟客户端
            highDelayOnlyCount++;
        } else {
            clientInfos[i].clientType = NORMAL;                  //普通客户端
            normalCount++;
        }
    }

    //输出客户端类型分布统计
    cout << "客户端类型分布:" << endl;
    cout << "  普通客户端: " << normalCount << " 个" << endl;
    cout << "  仅高流量客户端: " << highTrafficOnlyCount << " 个" << endl;
    cout << "  仅高延迟客户端: " << highDelayOnlyCount << " 个" << endl;
    cout << "  双属性客户端(高流量+高延迟): " << dualCount << " 个" << endl;
    cout << "  总计: " << (normalCount + highTrafficOnlyCount + highDelayOnlyCount + dualCount) << " 个" << endl;
    cout << "=== 特殊客户端类型初始化完成 ===\n" << endl;

    //步骤8：初始连接分配(前排2个AP分配65%，后排3个AP分配35%)
    //策略：AP0为门口AP，连接数要多于AP1，模拟真实教室环境
    vector<int> apStaCount(apInfos.size(), 0);             //各AP连接数统计
    vector<int> clientIdx(kNumClients);                    //客户端索引数组
    iota(clientIdx.begin(), clientIdx.end(), 0);           //填充0到kNumClients-1
    random_shuffle(clientIdx.begin(), clientIdx.end());    //随机打乱顺序

    int numFront = round(kNumClients * 0.65);              //前排65%的客户端
    int numBack = kNumClients - numFront;                  //后排35%的客户端

    //前排分配策略：AP0(门口)分配更多，AP1分配较少
    //AP0分配前排的60%，AP1分配前排的40%
    int ap0Num = round(numFront * 0.6);                    //门口AP0分配更多
    int ap1Num = numFront - ap0Num;                        //AP1分配剩余的

    cout << "\n=== 初始连接分配计划 ===" << endl;
    SimulationDelay(600);
    cout << "前排总数: " << numFront << " (65%)" << endl;
    SimulationDelay(200);
    cout << "  AP0(门口): " << ap0Num << " 个客户端" << endl;
    SimulationDelay(200);
    cout << "  AP1(前排): " << ap1Num << " 个客户端" << endl;
    SimulationDelay(200);
    cout << "后排总数: " << numBack << " (35%)" << endl;
    SimulationDelay(400);

    //后排3个AP平均分配策略
    vector<int> backApNum(3, numBack / 3);                //基础分配
    for (int i = 0; i < numBack % 3; ++i) {               //余数分配给前几个AP
        backApNum[i]++;
    }

    //执行客户端到AP的分配
    int idx = 0;                                          //客户端索引指针

    //AP0(门口AP)分配
    int cnt = 0;
    while (cnt < ap0Num && apStaCount[0] < apInfos[0].maxSta && idx < kNumClients) {
        int cli = clientIdx[idx++];                       //取下一个客户端
        clientInfos[cli].connectedApIdx = 0;              //连接到AP0
        apStaCount[0]++;                                  //更新AP0连接数
        cnt++;
    }
    cout << "AP0实际分配: " << cnt << " 个客户端 (计划: " << ap0Num << ")" << endl;
    SimulationDelay(300);

    //AP1(前排另一个AP)分配
    cnt = 0;
    while (cnt < ap1Num && apStaCount[1] < apInfos[1].maxSta && idx < kNumClients) {
        int cli = clientIdx[idx++];                       //取下一个客户端
        clientInfos[cli].connectedApIdx = 1;              //连接到AP1
        apStaCount[1]++;                                  //更新AP1连接数
        cnt++;
    }
    cout << "AP1实际分配: " << cnt << " 个客户端 (计划: " << ap1Num << ")" << endl;
    SimulationDelay(300);

    //后排AP(AP2, AP3, AP4)分配
    for (int ap = 0; ap < 3; ++ap) {
        int apIdx = ap + 2;                               //AP索引(2,3,4)
        cnt = 0;
        while (cnt < backApNum[ap] && apStaCount[apIdx] < apInfos[apIdx].maxSta && idx < kNumClients) {
            int cli = clientIdx[idx++];                   //取下一个客户端
            clientInfos[cli].connectedApIdx = apIdx;      //连接到对应后排AP
            apStaCount[apIdx]++;                          //更新AP连接数
            cnt++;
        }
    }

    //处理剩余未分配的客户端(如果有)，按信号强度分配
    for (; idx < kNumClients; ++idx) {
        int cli = clientIdx[idx];
        //记录每个AP的信号强度
        vector<pair<double, int>> rssiApIdx;
        for (size_t j = 0; j < apInfos.size(); ++j) {
            if (apStaCount[j] < apInfos[j].maxSta) {      //只考虑未满载的AP
                double rssi = CalcRssi(apInfos[j], clientInfos[cli]);
                rssiApIdx.push_back({rssi, (int)j});
            }
        }
        if (!rssiApIdx.empty()) {
            sort(rssiApIdx.rbegin(), rssiApIdx.rend());   //按信号强度降序排列
            int chosenAp = rssiApIdx[0].second;           //选择信号最强的AP
            clientInfos[cli].connectedApIdx = chosenAp;
            apStaCount[chosenAp]++;
        } else {
            clientInfos[cli].connectedApIdx = -1;         //无可用AP
        }
    }

    //步骤9：输出初始分配状态
    PrintApStaStatus("初始分配");
    PrintSpecialClients();

    //步骤10：第一层负载均衡(解除关联+重新分配)
    DisassociateAndRebalance();
    PrintApStaStatus("解除关联后");

    //步骤10.5：迟到学生仿真(上课迟到和上厕所回来的学生)
    SimulateLateStudents();
    PrintApStaStatus("迟到学生连接后");

    //步骤11：第二层特殊客户端负载均衡(处理高流量、高延迟、双属性客户端)
    SpecialClientBalance();
    PrintApStaStatus("特殊客户端均衡后");
    PrintBlacklist();

    //步骤12：输出最终负载均衡结果
    PrintApStaStatus("最终负载均衡结果");

    //步骤13：应用流量模型
    uint16_t port = 9;                                   
    ApplicationContainer serverApps, clientApps;         

    //为高流量客户端创建UDP服务器
    for (int i = 0; i < kNumHighTrafficClients; ++i) {
        int idx = *next(highTrafficIdx.begin(), i);      
        UdpServerHelper server(port + i);              
        serverApps.Add(server.Install(clientInfos[idx].node));  
    }
    serverApps.Start(Seconds(0.1));                      
    serverApps.Stop(Seconds(1.0));                       

    //为高流量客户端创建UDP客户端(流量发送方)
    for (int i = 0; i < kNumHighTrafficClients; ++i) {
        int idx = *next(highTrafficIdx.begin(), i);       
        UdpClientHelper client(clientInfos[idx].ip, port + i);  
        client.SetAttribute("MaxPackets", UintegerValue(10000));    
        client.SetAttribute("Interval", TimeValue(MilliSeconds(1))); 
        client.SetAttribute("PacketSize", UintegerValue(1024));      
        clientApps.Add(client.Install(apInfos[0].node));  
    }
    clientApps.Start(Seconds(0.2));                     
    clientApps.Stop(Seconds(1.0));                      

    //步骤14：启用FlowMonitor进行网络性能监控
    FlowMonitorHelper flowHelper;
    Ptr<FlowMonitor> flowMonitor = flowHelper.InstallAll();

    //启动仿真
    Simulator::Stop(Seconds(1.1));                        
    Simulator::Run();                                   
    //清理仿真环境并退出
    Simulator::Destroy();                               
    return 0;                                             
}

